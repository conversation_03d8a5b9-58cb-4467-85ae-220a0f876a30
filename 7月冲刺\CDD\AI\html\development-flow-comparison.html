<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>开发流程革命</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .glass-card {
            background: rgba(255,255,255,0.15);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255,255,255,0.3);
        }
        
        .flow-card {
            transition: all 0.3s ease;
        }
        
        .flow-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.3);
        }
        
        .traditional-flow {
            border-color: #dc2626;
            background: linear-gradient(135deg, rgba(220,38,38,0.2) 0%, rgba(185,28,28,0.1) 100%);
        }
        
        .ai-flow {
            border-color: #059669;
            background: linear-gradient(135deg, rgba(5,150,105,0.2) 0%, rgba(4,120,87,0.1) 100%);
        }
        
        .flow-step {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
        }
        
        .flow-step:hover {
            background: rgba(255,255,255,0.2);
            transform: scale(1.02);
        }
        
        .arrow {
            font-size: 2rem;
            color: rgba(255,255,255,0.8);
        }
        
        .cost-tag {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255,255,255,0.3);
        }
        
        @keyframes slideIn {
            from { opacity: 0; transform: translateX(-20px); }
            to { opacity: 1; transform: translateX(0); }
        }
        
        .slide-in {
            animation: slideIn 0.6s ease-out forwards;
        }
        
        .slide-in:nth-child(1) { animation-delay: 0.1s; }
        .slide-in:nth-child(2) { animation-delay: 0.2s; }
        .slide-in:nth-child(3) { animation-delay: 0.3s; }
        .slide-in:nth-child(4) { animation-delay: 0.4s; }
        .slide-in:nth-child(5) { animation-delay: 0.5s; }
    </style>
</head>
<body class="gradient-bg">
    <div class="container mx-auto px-6 py-12">
        <!-- 标题 -->
        <div class="text-center mb-16">
            <h1 class="text-5xl font-bold text-white mb-6">
                开发流程革命
            </h1>
            <h2 class="text-3xl font-semibold text-white/90 mb-4">
                第二个变化：新的开发流程
            </h2>
            <p class="text-xl text-white/80 max-w-4xl mx-auto">
                PRD、设计稿这种中间形态的产物，有没有可能是因为开发的成本太高了才产生的？
            </p>
        </div>
        
        <!-- 流程对比 -->
        <div class="space-y-12 max-w-7xl mx-auto">
            <!-- 传统开发流程 -->
            <div class="flow-card traditional-flow glass-card rounded-3xl p-8">
                <div class="text-center mb-8">
                    <div class="text-5xl mb-4">🏭</div>
                    <h3 class="text-2xl font-bold text-white mb-2">传统开发流程</h3>
                    <p class="text-red-200">复杂的中间环节，层层传递</p>
                </div>
                
                <div class="flex flex-wrap items-center justify-center gap-4">
                    <div class="flow-step rounded-xl p-4 text-center min-w-[120px] slide-in">
                        <div class="text-3xl mb-2">📝</div>
                        <div class="text-white font-semibold text-sm">需求</div>
                    </div>
                    
                    <div class="arrow slide-in">→</div>
                    
                    <div class="flow-step rounded-xl p-4 text-center min-w-[120px] slide-in">
                        <div class="text-3xl mb-2">👨‍💼</div>
                        <div class="text-white font-semibold text-sm">产品经理</div>
                    </div>
                    
                    <div class="arrow slide-in">→</div>
                    
                    <div class="flow-step rounded-xl p-4 text-center min-w-[120px] slide-in">
                        <div class="text-3xl mb-2">📋</div>
                        <div class="text-white font-semibold text-sm">PRD</div>
                    </div>
                    
                    <div class="arrow slide-in">→</div>
                    
                    <div class="flow-step rounded-xl p-4 text-center min-w-[120px] slide-in">
                        <div class="text-3xl mb-2">🔄</div>
                        <div class="text-white font-semibold text-sm">改PRD</div>
                    </div>
                    
                    <div class="arrow slide-in">→</div>
                    
                    <div class="flow-step rounded-xl p-4 text-center min-w-[120px] slide-in">
                        <div class="text-3xl mb-2">🎨</div>
                        <div class="text-white font-semibold text-sm">设计稿</div>
                    </div>
                    
                    <div class="arrow slide-in">→</div>
                    
                    <div class="flow-step rounded-xl p-4 text-center min-w-[120px] slide-in">
                        <div class="text-3xl mb-2">🔄</div>
                        <div class="text-white font-semibold text-sm">改设计稿</div>
                    </div>
                    
                    <div class="arrow slide-in">→</div>
                    
                    <div class="flow-step rounded-xl p-4 text-center min-w-[120px] slide-in">
                        <div class="text-3xl mb-2">💻</div>
                        <div class="text-white font-semibold text-sm">前端代码</div>
                    </div>
                </div>
                
                <div class="mt-8 grid md:grid-cols-3 gap-4">
                    <div class="cost-tag rounded-lg p-3 text-center">
                        <div class="text-red-300 font-semibold">改PRD: 1小时</div>
                    </div>
                    <div class="cost-tag rounded-lg p-3 text-center">
                        <div class="text-red-300 font-semibold">改设计稿: 半天</div>
                    </div>
                    <div class="cost-tag rounded-lg p-3 text-center">
                        <div class="text-red-300 font-semibold">改代码: 1-2周</div>
                    </div>
                </div>
            </div>
            
            <!-- VS 分隔符 -->
            <div class="text-center">
                <div class="glass-card inline-block rounded-full px-8 py-4">
                    <span class="text-4xl font-bold text-white">VS</span>
                </div>
            </div>
            
            <!-- AI驱动流程 -->
            <div class="flow-card ai-flow glass-card rounded-3xl p-8">
                <div class="text-center mb-8">
                    <div class="text-5xl mb-4">🤖</div>
                    <h3 class="text-2xl font-bold text-white mb-2">AI驱动流程</h3>
                    <p class="text-green-200">跳过中间环节，直达结果</p>
                </div>
                
                <div class="flex flex-wrap items-center justify-center gap-4">
                    <div class="flow-step rounded-xl p-4 text-center min-w-[140px] slide-in">
                        <div class="text-3xl mb-2">📝</div>
                        <div class="text-white font-semibold text-sm">需求</div>
                    </div>
                    
                    <div class="arrow slide-in">→</div>
                    
                    <div class="flow-step rounded-xl p-4 text-center min-w-[140px] slide-in">
                        <div class="text-3xl mb-2">🎯</div>
                        <div class="text-white font-semibold text-sm">提示词1<br><span class="text-xs text-white/70">需求拆分专家</span></div>
                    </div>
                    
                    <div class="arrow slide-in">→</div>
                    
                    <div class="flow-step rounded-xl p-4 text-center min-w-[140px] slide-in">
                        <div class="text-3xl mb-2">📐</div>
                        <div class="text-white font-semibold text-sm">提示词2<br><span class="text-xs text-white/70">原型说明专家</span></div>
                    </div>
                    
                    <div class="arrow slide-in">→</div>
                    
                    <div class="flow-step rounded-xl p-4 text-center min-w-[140px] slide-in">
                        <div class="text-3xl mb-2">⚡</div>
                        <div class="text-white font-semibold text-sm">提示词3<br><span class="text-xs text-white/70">原型生成专家</span></div>
                    </div>
                    
                    <div class="arrow slide-in">→</div>
                    
                    <div class="flow-step rounded-xl p-4 text-center min-w-[140px] slide-in">
                        <div class="text-3xl mb-2">💻</div>
                        <div class="text-white font-semibold text-sm">前端代码</div>
                    </div>
                </div>
                
                <div class="mt-8 grid md:grid-cols-3 gap-4">
                    <div class="cost-tag rounded-lg p-3 text-center">
                        <div class="text-green-300 font-semibold">改提示词: 10分钟</div>
                    </div>
                    <div class="cost-tag rounded-lg p-3 text-center">
                        <div class="text-green-300 font-semibold">重新生成: 5分钟</div>
                    </div>
                    <div class="cost-tag rounded-lg p-3 text-center">
                        <div class="text-green-300 font-semibold">测试验证: 30分钟</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 核心洞察 -->
        <div class="text-center mt-16 mb-12">
            <div class="glass-card rounded-2xl p-8 max-w-5xl mx-auto">
                <h3 class="text-3xl font-bold text-white mb-6">
                    💡 核心洞察
                </h3>
                <div class="bg-white/10 rounded-xl p-6 mb-6">
                    <p class="text-xl text-white/90 leading-relaxed">
                        <span class="text-yellow-300 font-bold">PRD、设计稿</span>这种中间形态的产物，<br>
                        本质上是因为<span class="text-red-300 font-bold">改代码成本太高</span>才产生的妥协方案
                    </p>
                </div>
                <div class="grid md:grid-cols-2 gap-6">
                    <div class="bg-red-500/20 rounded-xl p-6">
                        <h4 class="text-lg font-bold text-red-300 mb-3">传统逻辑</h4>
                        <p class="text-white/80 text-sm text-left">
                            改代码成本高 → 需要文档缓冲 → 先改文档再改代码 → 层层传递降低风险
                        </p>
                    </div>
                    <div class="bg-green-500/20 rounded-xl p-6">
                        <h4 class="text-lg font-bold text-green-300 mb-3">AI时代逻辑</h4>
                        <p class="text-white/80 text-sm text-left">
                            改代码成本低 → 文档变成包袱 → 直接改提示词重新生成 → 快速迭代验证
                        </p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 关键问题 -->
        <div class="text-center">
            <div class="glass-card rounded-2xl p-8 max-w-4xl mx-auto">
                <h3 class="text-2xl font-bold text-white mb-4">
                    🤔 关键问题
                </h3>
                <p class="text-xl text-white/90 leading-relaxed mb-4">
                    当改代码比改文档还快的时候，<br>
                    我们还需要那些"中间环节"吗？
                </p>
                <div class="bg-white/10 rounded-xl p-6">
                    <p class="text-lg text-green-300 font-bold">
                        版本迭代不再是改PRD、改设计稿、改代码<br>
                        而是改提示词1、提示词2、提示词3，改完重新生成
                    </p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
