<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>开发成本对比分析</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .glass-card {
            background: rgba(255,255,255,0.15);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255,255,255,0.3);
        }
        
        .cost-card {
            transition: all 0.3s ease;
        }
        
        .cost-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.3);
        }
        
        .traditional-card {
            border-color: #ef4444;
            background: linear-gradient(135deg, rgba(239,68,68,0.2) 0%, rgba(220,38,38,0.1) 100%);
        }
        
        .ai-card {
            border-color: #10b981;
            background: linear-gradient(135deg, rgba(16,185,129,0.2) 0%, rgba(5,150,105,0.1) 100%);
        }
        
        .cost-number {
            font-size: 3rem;
            font-weight: 900;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .vs-divider {
            background: rgba(255,255,255,0.2);
            backdrop-filter: blur(10px);
        }
        
        @keyframes pulse-red {
            0%, 100% { box-shadow: 0 0 0 0 rgba(239,68,68,0.7); }
            70% { box-shadow: 0 0 0 10px rgba(239,68,68,0); }
        }
        
        @keyframes pulse-green {
            0%, 100% { box-shadow: 0 0 0 0 rgba(16,185,129,0.7); }
            70% { box-shadow: 0 0 0 10px rgba(16,185,129,0); }
        }
        
        .pulse-red {
            animation: pulse-red 2s infinite;
        }
        
        .pulse-green {
            animation: pulse-green 2s infinite;
        }
    </style>
</head>
<body class="gradient-bg">
    <div class="container mx-auto px-6 py-12">
        <!-- 标题 -->
        <div class="text-center mb-16">
            <h1 class="text-5xl font-bold text-white mb-6">
                开发成本革命
            </h1>
            <h2 class="text-3xl font-semibold text-white/90 mb-4">
                传统团队 vs AI驱动
            </h2>
            <p class="text-xl text-white/80 max-w-3xl mx-auto">
                大模型是新技术，新技术做老场景，有没有可能把成本降低10倍，降低100倍？
            </p>
        </div>
        
        <!-- 成本对比 -->
        <div class="grid lg:grid-cols-2 gap-8 max-w-6xl mx-auto mb-12">
            <!-- 传统团队成本 -->
            <div class="cost-card traditional-card glass-card rounded-3xl p-8 text-center pulse-red">
                <div class="mb-6">
                    <span class="text-6xl">👥</span>
                </div>
                <h3 class="text-2xl font-bold text-white mb-4">传统团队开发</h3>
                
                <!-- 人员构成 -->
                <div class="mb-6 space-y-3">
                    <div class="flex justify-between items-center bg-white/10 rounded-lg p-3">
                        <span class="text-white/90">产品经理</span>
                        <span class="text-red-200 font-semibold">15k/月</span>
                    </div>
                    <div class="flex justify-between items-center bg-white/10 rounded-lg p-3">
                        <span class="text-white/90">前端工程师</span>
                        <span class="text-red-200 font-semibold">18k/月</span>
                    </div>
                    <div class="flex justify-between items-center bg-white/10 rounded-lg p-3">
                        <span class="text-white/90">后端工程师</span>
                        <span class="text-red-200 font-semibold">20k/月</span>
                    </div>
                    <div class="flex justify-between items-center bg-white/10 rounded-lg p-3">
                        <span class="text-white/90">UI设计师</span>
                        <span class="text-red-200 font-semibold">12k/月</span>
                    </div>
                </div>
                
                <!-- 总成本 -->
                <div class="border-t border-white/20 pt-4 mb-6">
                    <div class="text-white/80 mb-2">月人力成本：6.5万</div>
                    <div class="text-white/80 mb-4">开发周期：2-3个月</div>
                    <div class="cost-number text-red-300">15-20万</div>
                    <div class="text-red-200 font-semibold">人力投入</div>
                </div>
                
                <!-- 风险提示 -->
                <div class="bg-red-500/20 rounded-xl p-4">
                    <p class="text-red-200 font-semibold mb-2">⚠️ 高风险</p>
                    <p class="text-red-100 text-sm">成本确定，收入未知</p>
                </div>
            </div>
            
            <!-- AI驱动成本 -->
            <div class="cost-card ai-card glass-card rounded-3xl p-8 text-center pulse-green">
                <div class="mb-6">
                    <span class="text-6xl">👨‍💻</span>
                </div>
                <h3 class="text-2xl font-bold text-white mb-4">1个人 + 提示词 + AI</h3>
                
                <!-- 资源构成 -->
                <div class="mb-6 space-y-3">
                    <div class="flex justify-between items-center bg-white/10 rounded-lg p-3">
                        <span class="text-white/90">你+产品提示词+AI</span>
                        <span class="text-green-200 font-semibold">50元/月</span>
                    </div>
                    <div class="flex justify-between items-center bg-white/10 rounded-lg p-3">
                        <span class="text-white/90">你+前端提示词+AI</span>
                        <span class="text-green-200 font-semibold">50元/月</span>
                    </div>
                    <div class="flex justify-between items-center bg-white/10 rounded-lg p-3">
                        <span class="text-white/90">你+后端提示词+AI</span>
                        <span class="text-green-200 font-semibold">50元/月</span>
                    </div>
                    <div class="flex justify-between items-center bg-white/10 rounded-lg p-3">
                        <span class="text-white/90">你+设计提示词+AI</span>
                        <span class="text-green-200 font-semibold">50元/月</span>
                    </div>
                </div>

                <!-- 总成本 -->
                <div class="border-t border-white/20 pt-4 mb-6">
                    <div class="text-white/80 mb-2">月AI调用成本：200元</div>
                    <div class="text-white/80 mb-4">开发周期：1-2周</div>
                    <div class="cost-number text-green-300">200元</div>
                    <div class="text-green-200 font-semibold">月总成本</div>
                </div>
                
                <!-- 优势提示 -->
                <div class="bg-green-500/20 rounded-xl p-4">
                    <p class="text-green-200 font-semibold mb-2">✅ 325倍成本优势</p>
                    <p class="text-green-100 text-sm">提示词工程师 = 全栈团队</p>
                </div>
            </div>
        </div>
        
        <!-- VS 分隔符 -->
        <div class="text-center mb-12">
            <div class="vs-divider inline-block rounded-full px-8 py-4">
                <span class="text-4xl font-bold text-white">VS</span>
            </div>
        </div>
        
        <!-- 成本降低倍数 -->
        <div class="text-center mb-12">
            <div class="glass-card rounded-2xl p-8 max-w-4xl mx-auto">
                <h3 class="text-3xl font-bold text-white mb-6">
                    📉 成本降低
                </h3>
                <div class="grid md:grid-cols-3 gap-6">
                    <div class="bg-white/10 rounded-xl p-6">
                        <div class="text-4xl font-bold text-yellow-300 mb-2">325倍</div>
                        <div class="text-white/80">成本降低</div>
                        <div class="text-xs text-white/60 mt-1">6.5万 → 200元</div>
                    </div>
                    <div class="bg-white/10 rounded-xl p-6">
                        <div class="text-4xl font-bold text-blue-300 mb-2">10倍</div>
                        <div class="text-white/80">开发速度提升</div>
                        <div class="text-xs text-white/60 mt-1">2-3个月 → 1-2周</div>
                    </div>
                    <div class="bg-white/10 rounded-xl p-6">
                        <div class="text-4xl font-bold text-purple-300 mb-2">1人</div>
                        <div class="text-white/80">替代4人团队</div>
                        <div class="text-xs text-white/60 mt-1">提示词工程师</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 核心问题 -->
        <div class="text-center">
            <div class="glass-card rounded-2xl p-8 max-w-4xl mx-auto">
                <h3 class="text-2xl font-bold text-white mb-4">
                    🤔 关键问题
                </h3>
                <p class="text-xl text-white/90 leading-relaxed">
                    有没有可能1个人把产品、设计、前端、后端的活都给干了？<br>
                    有没有可能通过VibeCoding把一个产品做出来？
                </p>
                <div class="mt-6 text-2xl font-bold text-green-300">
                    答案：做出来了！
                </div>
            </div>
        </div>
    </div>
</body>
</html>
