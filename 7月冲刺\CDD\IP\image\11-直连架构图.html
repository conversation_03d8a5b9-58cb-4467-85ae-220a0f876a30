<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>直连架构图</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            max-width: 1000px;
            width: 100%;
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .title {
            text-align: center;
            font-size: 28px;
            color: #2c3e50;
            margin-bottom: 40px;
            font-weight: bold;
        }
        
        .architecture-diagram {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 30px;
            margin: 40px 0;
            flex-wrap: wrap;
        }
        
        .node {
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
            color: white;
            padding: 30px 25px;
            border-radius: 15px;
            text-align: center;
            min-width: 150px;
            box-shadow: 0 15px 30px rgba(78, 205, 196, 0.3);
            position: relative;
            transition: all 0.3s ease;
        }
        
        .node:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(78, 205, 196, 0.4);
        }
        
        .node-china {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            box-shadow: 0 15px 30px rgba(255, 107, 107, 0.3);
        }
        
        .node-china:hover {
            box-shadow: 0 20px 40px rgba(255, 107, 107, 0.4);
        }
        
        .node-us {
            background: linear-gradient(135deg, #45b7d1, #96c93d);
            box-shadow: 0 15px 30px rgba(69, 183, 209, 0.3);
        }
        
        .node-us:hover {
            box-shadow: 0 20px 40px rgba(69, 183, 209, 0.4);
        }
        
        .node-target {
            background: linear-gradient(135deg, #667eea, #764ba2);
            box-shadow: 0 15px 30px rgba(102, 126, 234, 0.3);
        }
        
        .node-target:hover {
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.4);
        }
        
        .node-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .node-desc {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 15px;
        }
        
        .node-flag {
            font-size: 32px;
            margin-top: 10px;
            display: block;
        }
        
        .arrow {
            font-size: 36px;
            color: #3498db;
            font-weight: bold;
            display: flex;
            align-items: center;
            position: relative;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        
        .arrow::before {
            content: '';
            position: absolute;
            top: 50%;
            left: -15px;
            right: -15px;
            height: 3px;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            transform: translateY(-50%);
            z-index: -1;
            border-radius: 2px;
        }
        
        .connection-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-top: 40px;
        }
        
        .info-title {
            font-size: 20px;
            color: #2c3e50;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
        }
        
        .info-box {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #3498db;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        
        .info-label {
            font-size: 14px;
            color: #7f8c8d;
            margin-bottom: 8px;
        }
        
        .info-value {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .advantages {
            margin-top: 40px;
        }
        
        .advantages-title {
            font-size: 20px;
            color: #2c3e50;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .advantages-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .advantage-box {
            background: #e8f5e8;
            border-left: 4px solid #27ae60;
            padding: 20px;
            border-radius: 8px;
        }
        
        .advantage-title {
            font-size: 16px;
            font-weight: bold;
            color: #27ae60;
            margin-bottom: 10px;
        }
        
        .advantage-desc {
            color: #2c3e50;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .limitations {
            margin-top: 40px;
        }
        
        .limitations-title {
            font-size: 20px;
            color: #2c3e50;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .limitations-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .limitation-box {
            background: #fdf2f2;
            border-left: 4px solid #e74c3c;
            padding: 20px;
            border-radius: 8px;
        }
        
        .limitation-title {
            font-size: 16px;
            font-weight: bold;
            color: #e74c3c;
            margin-bottom: 10px;
        }
        
        .limitation-desc {
            color: #2c3e50;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .emoji {
            font-size: 18px;
            margin-right: 8px;
        }
        
        @media (max-width: 768px) {
            .architecture-diagram {
                flex-direction: column;
                gap: 20px;
            }
            
            .arrow {
                transform: rotate(90deg);
                font-size: 24px;
            }
            
            .arrow::before {
                top: -15px;
                bottom: -15px;
                left: 50%;
                right: auto;
                width: 3px;
                height: auto;
                transform: translateX(-50%);
            }
            
            .info-grid,
            .advantages-grid,
            .limitations-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="title">直连架构：简单但有限制</div>
        
        <div class="architecture-diagram">
            <div class="node node-china">
                <div class="node-title">你的设备</div>
                <div class="node-desc">手机/电脑</div>
                <span class="node-flag">🇨🇳</span>
            </div>
            
            <div class="arrow">→</div>
            
            <div class="node node-us">
                <div class="node-title">美国VPS</div>
                <div class="node-desc">云服务器</div>
                <span class="node-flag">🇺🇸</span>
            </div>
            
            <div class="arrow">→</div>
            
            <div class="node node-target">
                <div class="node-title">TikTok服务器</div>
                <div class="node-desc">目标平台</div>
                <span class="node-flag">🇺🇸</span>
            </div>
        </div>
        
        <div class="connection-info">
            <div class="info-title">连接信息</div>
            <div class="info-grid">
                <div class="info-box">
                    <div class="info-label">物理距离</div>
                    <div class="info-value">1万+公里</div>
                </div>
                <div class="info-box">
                    <div class="info-label">预期延迟</div>
                    <div class="info-value">300ms+</div>
                </div>
                <div class="info-box">
                    <div class="info-label">月度成本</div>
                    <div class="info-value">$10</div>
                </div>
            </div>
        </div>
        
        <div class="advantages">
            <div class="advantages-title">✅ 优势</div>
            <div class="advantages-grid">
                <div class="advantage-box">
                    <div class="advantage-title">
                        <span class="emoji">💰</span>成本低廉
                    </div>
                    <div class="advantage-desc">
                        单台VPS成本低，适合小规模测试使用
                    </div>
                </div>
                
                <div class="advantage-box">
                    <div class="advantage-title">
                        <span class="emoji">🔧</span>配置简单
                    </div>
                    <div class="advantage-desc">
                        架构简单，配置和维护都比较容易
                    </div>
                </div>
                
                <div class="advantage-box">
                    <div class="advantage-title">
                        <span class="emoji">🌍</span>解决地理限制
                    </div>
                    <div class="advantage-desc">
                        TikTok看到的是美国IP，可以正常访问
                    </div>
                </div>
                
                <div class="advantage-box">
                    <div class="advantage-title">
                        <span class="emoji">🔒</span>独立IP
                    </div>
                    <div class="advantage-desc">
                        每台VPS都有独立IP，不会被其他用户影响
                    </div>
                </div>
            </div>
        </div>
        
        <div class="limitations">
            <div class="limitations-title">❌ 局限性</div>
            <div class="limitations-grid">
                <div class="limitation-box">
                    <div class="limitation-title">
                        <span class="emoji">🐌</span>延迟过高
                    </div>
                    <div class="limitation-desc">
                        300ms+的延迟，影响用户体验，特别是直播场景
                    </div>
                </div>
                
                <div class="limitation-box">
                    <div class="limitation-title">
                        <span class="emoji">📊</span>不稳定
                    </div>
                    <div class="limitation-desc">
                        跨国网络不稳定，容易出现丢包和断线问题
                    </div>
                </div>
                
                <div class="limitation-box">
                    <div class="limitation-title">
                        <span class="emoji">💸</span>规模成本高
                    </div>
                    <div class="limitation-desc">
                        1000个账号需要1000台VPS，总成本$10,000/月
                    </div>
                </div>
                
                <div class="limitation-box">
                    <div class="limitation-title">
                        <span class="emoji">🔧</span>维护复杂
                    </div>
                    <div class="limitation-desc">
                        大量服务器的管理和维护工作量巨大
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
