# 第1部分：提示词和MCP工具设计

## 🎯 核心理念

**RAAS模式**：用户提问 → AI分析 → 直接给出结果
- 用户不需要学习工具使用
- AI自动调用工具获取数据
- AI基于数据进行推理分析
- 直接输出可执行的商业洞察

## 🛠️ MCP工具架构设计

### 工具分类原则

**只设计两类工具：**
1. **数据获取工具** - 纯粹的数据访问，不做分析
2. **结果展示工具** - 纯粹的可视化生成，不做分析

**所有分析推理由大模型完成，不设计分析类工具**

### 具体工具设计

#### 1. 广告数据查询工具 (AdDataQueryMCP)

**功能定位：**
- 查询用户的个人广告数据库
- 支持多维度筛选和搜索
- 返回结构化的原始数据

**输入参数：**
```python
{
    "user_id": "用户标识",
    "date_range": "时间范围（如'最近30天'、'2024年1月'）",
    "platform": "平台筛选（Facebook/TikTok/小红书/抖音/all）",
    "keywords": ["关键词列表"],
    "category": "行业分类（可选）",
    "sort_by": "排序方式（时间/热度/相关性）",
    "limit": "返回数量限制"
}
```

**输出数据：**
```python
{
    "total_count": "总数量",
    "ads": [
        {
            "id": "广告ID",
            "content": "广告内容（文案、图片、视频）",
            "platform": "投放平台",
            "timestamp": "时间戳",
            "user_interaction": "用户交互数据（点击、保存等）",
            "raw_tags": "原始标签信息"
        }
    ],
    "query_info": "查询条件信息"
}
```

#### 2. 可视化生成工具 (VisualizationMCP)

**功能定位：**
- 根据分析结果生成可视化内容
- 支持多种图表和页面类型
- 提供交互式展示功能

**输入参数：**
```python
{
    "data": "待可视化的数据",
    "viz_type": "可视化类型",
    "title": "标题",
    "style": "样式配置",
    "interactive": "是否需要交互功能"
}
```

**可视化类型：**
- `trend_chart`: 趋势分析图
- `opportunity_dashboard`: 机会分析仪表板
- `comparison_chart`: 对比分析图
- `category_distribution`: 分类分布图
- `timeline_view`: 时间线视图

**输出结果：**
```python
{
    "html_url": "生成的页面链接",
    "preview_image": "预览图片",
    "interactive_features": ["可用的交互功能"],
    "export_options": ["导出选项"]
}
```

## 💭 提示词设计

### 核心分析提示词

```python
CORE_ANALYSIS_PROMPT = """
你是专业的广告数据分析师，专门帮助用户从个人广告数据中发现商业机会。

## 分析框架
基于用户的个人广告浏览数据，从以下角度进行分析：
1. **新兴机会识别** - 发现新出现或快速增长的商业模式
2. **趋势变化分析** - 识别行业投放策略和内容的变化
3. **竞品动态监控** - 分析竞争对手的广告策略变化
4. **可行性评估** - 基于用户背景评估机会的可操作性

## 用户查询
{user_query}

## 广告数据
{ad_data}

## 用户背景
{user_context}

## 分析要求
1. 基于实际数据，不要臆测
2. 提供具体的数字和案例
3. 给出可执行的建议
4. 标注风险和注意事项
5. 使用结构化格式输出

请开始分析：
"""
```

### 场景化提示词模板

#### 1. 机会发现场景
```python
OPPORTUNITY_DISCOVERY_PROMPT = """
基于用户最近{time_period}的广告数据，识别新的商业机会：

数据概况：
- 总广告数：{total_ads}
- 涉及平台：{platforms}
- 主要类别：{categories}

分析重点：
1. 识别出现频次增长超过{growth_threshold}%的新品类
2. 发现重复出现的新兴商业模式
3. 分析定价策略和获客方式的变化
4. 评估进入门槛和竞争程度

输出格式：
🔥 **机会1：[机会名称]**
- 出现频次：X次，较上期增长X%
- 主要玩家：[品牌列表]
- 商业模式：[模式描述]
- 机会分析：[具体分析]

[继续其他机会...]
"""
```

#### 2. 趋势分析场景
```python
TREND_ANALYSIS_PROMPT = """
分析{category}类广告在{time_period}的变化趋势：

对比数据：
- 当期数据：{current_data}
- 历史对比：{historical_data}

分析维度：
1. 内容创意的变化趋势
2. 投放策略的演变
3. 目标用户群体的变化
4. 定价模式的调整

输出格式：
📈 **趋势变化总结**
- 核心变化：[主要变化点]
- 影响因素：[驱动因素]
- 未来预测：[趋势预判]

📊 **具体数据分析**
[详细的数据对比和分析]
"""
```

#### 3. 竞品监控场景
```python
COMPETITOR_ANALYSIS_PROMPT = """
分析{competitor_focus}在{platform}平台的广告策略：

监控数据：
- 广告投放量：{ad_volume}
- 主推产品：{main_products}
- 投放时间：{timing_pattern}

分析重点：
1. 广告创意和文案策略
2. 产品定位和卖点变化
3. 目标用户群体分析
4. 投放节奏和预算估算

输出格式：
🎯 **竞品策略分析**
- 核心策略：[策略总结]
- 变化趋势：[策略变化]
- 学习价值：[可借鉴点]
- 差异化机会：[竞争空白]
"""
```

## 🔄 工具调用逻辑

### 标准分析流程

```python
def standard_analysis_workflow(user_query, user_context):
    """标准的广告数据分析流程"""
    
    # 1. 理解用户意图
    intent = parse_user_intent(user_query)
    
    # 2. 构建数据查询参数
    query_params = build_query_params(intent, user_context)
    
    # 3. 调用数据查询工具
    ad_data = call_tool("AdDataQueryMCP", query_params)
    
    # 4. 选择合适的分析提示词
    prompt_template = select_analysis_prompt(intent)
    
    # 5. 执行大模型分析
    analysis_result = llm_analyze(prompt_template, ad_data, user_context)
    
    # 6. 生成可视化（如果需要）
    if intent.need_visualization:
        viz_params = build_viz_params(analysis_result, intent)
        visualization = call_tool("VisualizationMCP", viz_params)
    
    return analysis_result, visualization
```

### 意图识别逻辑

```python
INTENT_PATTERNS = {
    "opportunity_discovery": [
        "新机会", "商业机会", "有什么机会", "新的趋势"
    ],
    "trend_analysis": [
        "趋势", "变化", "发展", "演变", "对比"
    ],
    "competitor_monitoring": [
        "竞品", "竞争对手", "同行", "对手分析"
    ],
    "category_analysis": [
        "行业分析", "类别分析", "领域分析"
    ]
}

def parse_user_intent(user_query):
    """解析用户意图"""
    intent = {
        "type": "general_analysis",  # 默认类型
        "time_range": extract_time_range(user_query),
        "platform": extract_platform(user_query),
        "keywords": extract_keywords(user_query),
        "need_visualization": True
    }
    
    # 根据关键词识别具体意图类型
    for intent_type, keywords in INTENT_PATTERNS.items():
        if any(keyword in user_query for keyword in keywords):
            intent["type"] = intent_type
            break
    
    return intent
```

## 🎯 设计原则总结

1. **工具职责单一**：数据获取和结果展示，不做分析
2. **分析逻辑集中**：所有智能分析都在提示词中
3. **场景化设计**：针对不同场景设计专门的提示词
4. **标准化流程**：统一的工具调用和分析流程
5. **可扩展架构**：容易添加新的工具和分析场景

这样的设计让你可以专注于优化分析质量，而不是复杂的技术架构。
