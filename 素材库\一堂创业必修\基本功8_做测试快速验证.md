# 基本功8：做测试|快速验证  

面对创业待验证的风险（即关键假设），如果行业常识和竞争调研都不能解决问题，下一步，我建议你采用精益的方法来做实验测试。  

什么是精益呢？核心就是小步快跑，快速试错。尽量避免在一开始，就做一个大而全的产品，一次性就把所有资源都赌上，企图把所有验证工作一次性都做完。  

关于这种创业方法论，我们推荐三本书：第一本是《精益创业》，在创业圈已广为人知；第二本是《精益创业实战》，里面有很多实操建议；第三本是《精益商业思维》，作者是迅雷创始人程浩。  

程浩是国内精益话题最好的分享者，作为一堂的荣誉校长，程浩和我们一起打磨了一套精益的实操版课程。面对一些需求不确定，高风险的业务，可以用精益五步法”快速提升业务测试效率。  

训练任务：避免大而全的产品开发，面对高风险的创业方向，极力避免憋大招的情况，用小步快跑的实验，来不断测试，不断试错转型，最终快速跑通商业模式。  

训练工具：  


<html><body><table><tr><td>工具</td><td>类型</td><td>训练目的</td></tr><tr><td>精益验证模型</td><td>方法模型（Framework)</td><td>掌握精益验证的5个步骤</td></tr><tr><td>MVP设计武器库</td><td>方法模型（Framework）</td><td>掌握低成本的验证方法</td></tr></table></body></html>  

这个模块相对比较硬，建议快速熟悉方法论以后，认真复盘几轮自己的业务或者身边朋友的业务，然后回答一个问题：如果现在重新操盘测试，你会怎么做？多做几次案例代入的创业推演”，可以帮你更好地掌握精益这套创业方法论。  

# 88以太一堂·内部训练手册  

# 模型：精益验证模型  

很多人可能对于精益的概念很熟悉，但不知道怎么落地。对于这个问题，我们总结了精益创业落地的五个关键环节，抓住这几个环节，你的精益工作就能算合格了。  

这五个关键环节分别是：  

1.快速探索需求：初步验证一下用户的需求到底痛不痛。  
2.验证用户方案：设计一个MVP来直接测试方案可行性。  
3.做好数据判断：拿到信息以后，如何分析数据。  
4.决定转型还是坚持：如果验证不顺利想转型，你可以有哪些转型的思路。  
5.加速增长扩张：如果模型跑通，如何加速扩张。  

# 第一步：快速探索需求  

这里有三个常见方法：第一个是痛点分析，第二个是团队头脑风暴，第三个是寻找核心用户。谁是核心用户呢？“谁最痛，谁就是核心用户”。  

# 第二步：验证用户方案  

MVP 从成本最低到最高的演化过程，一共有五条关键策略，在后面的“MVP设计武器库框架”里展开进行了介绍。在这里，我们先分享妨碍创业者做好MVP的四个常见心态。  

心态1：完美主义倾向，总想把最全、最好的产品或者版本给用户。  
心态2：担心被大公司或竞争对手抄袭。  
心态3：担心品牌风险，一般来说只有大公司才会担心推出一款MVP，因为不成熟甚至比较差，会拖累公司的品牌。  
心态4：担心MVP不成功，导致团队不稳定。  

# 第三步：做好数据判断  

这里有四个常用工具。  

第一个工具，是转化漏斗分析。这个对于电商特别常用。  
第二个工具，是北极星指标和海盗模型。这是增长黑客理论里面，最重要的两个指标工具。  
第三个工具，叫CohortAnalysis，有人翻译为同期群分析，我们把它称为切片分析，  

CohortAnalysis做留存率分析特别方便。  

第四个数据分析工具，是A/B测试。一个产品设计可能有两套方案，究竟A好还是B好，谁也说不清楚，怎么办？此时可以用A/B测试。  

强调下，这些数据工具都只是工具，是术”层面的东西，肯后的核心还是你要懂业务模型，以及行业里的Benchmark。  

# 第四步：决定转型还是坚持  

如果你在第三步，判断商业模式成立，那恭喜你，可以进入第五步，开始复制扩张，获取更多的用户。  

但现实情况是，有很大的概率，你的前几版方案都是不成立的，你需要不断试错、调整和转型，才能逐步接近最终那个成立的模式。  

创业户最常见的7种转型：  

第一种：精简型，做小而美；  

第二种：扩充型，做全套；  

第三种：ToC转ToB；  

第四种：自营转平台；  

第五种：收费转免费；  

第六种：代理转直销或者直销转代理；  

第七种：技术方案转型  

# 第五步：加速增长扩张  

增长是个不亚于精益的体系，通常《精益创业》的书都对增长讲得很浅，因为不同项目的增长方式差异极大。这里不展开了，推荐几个话题：增长黑客、销售铁军、私域流量、投放买量、病毒营销。  

希望你能清楚精益的执行框架，如果从0启动一个业务，你该怎么一步步推进验证。先探索需求找痛点，再设计MVP验证，然后做好数据判断，如果没跑通怎么调整或转型，最后如果跑通了，开始增长扩张。  

# 90|以太一堂·内部训练手册  

# 模型：MVP 设计武器库  

很多创业者虽然看了很多精益方面的书，自以为掌握了其精髓，但是落地实操后，还是老样子，开发一个大而全的产品。  

发生这种情况，很多时候，还是创业者没懂怎么设计MVP。客观来说，能否设计一个最低成本、最便宜甚至可以说最聪明的MVP，来验证自己的创业方案，是很考验大家的功力的。  

为了解决这个问题，我们提供了一个原则，设计MVP实验，在能验证假设的情况下，要越快越好、越简单越好，越直接越好。在这个原则下，我们提供MVP设计武器库框架（见下图），这张图总共涵盖了5个策略，从左往右，开发成本越来越高。  

当我们面临MVP设计时，都要反复问自己一个问题：是不是可以先选择更左侧的方式，如果真的跑通了，再切换右边的方法继续测试，随着确定性变高，投入资源也越来越高，这才是一个真正优秀的创业路径。  

# MVP设计武器库：  

![](images/f3001eff9ac1eaf3ac922cb41318c5cda23f09b17bf5652f8e72dce88dc4270c.jpg)  
从左到右的MVP成本越来越高  

这个MVP设计策略的武器库，是一堂教研组收集了国内国外能找到的所有MVP精彩案例，再进行分门别类并寻找规律，最后研制而成的。  

大家未来要是遇到MVP设计的难题，可以从武器库里的几十个案例选出跟自己项目相近的案例，匹配后，进行后续的测试。  
