# 如何做好复杂项目的管理

## 引言：耐烦的重要性

跟大家快速地说一下怎么样去做一个复杂的协作工作。什么叫复杂的协作工作呢？在曾国藩的家书里面，他给他的九弟（曾国权）曾经多次写过信，里面就有一个关键词叫"耐烦"。曾国权是个猛将，非常的勇猛，但是他有一个特点就是脾气比较暴躁，缺乏耐心。

"耐烦"是指可以忍受琐碎繁杂的事务和干扰，所以曾国藩强调他要"耐得烦"，就是我们经常所说的愿意把手弄湿、把手搞脏，这其实是一个人非常重要的品质。

## 复杂项目的特点

什么叫复杂的事情？我总结了一下，大概是这么几个特点：
- 多线并行
- 干系人众多
- 情况很庞杂
- 节奏不统一

就这么四个要素。很多人就说："你为什么可以在这个公司里面工作？"我说："很简单，我耐得烦，就能干脏活、累活，就这么简单。"

## 三个关键建议

### 建议一：倒着做清单，不要正着做计划

我稍微给大家分享一下我的经验，就是要倒着做清单，不要正着做计划。为什么我们同事很多时候拿了第一个方案文档，在我这里通不过？因为99%的人做方案都是从今天往后推导，这样做肯定是做不成事的，尤其是复杂项目一定做不成。

为什么？因为他一定受限于你的经验，你最大的想象力就是你的经验。那如果你的经验能覆盖这件事的复杂性，那这件事也就不复杂了。

如果以跨年演讲为例，假如你是策划这个活动，你自己思考这个问题的起点应该在哪儿？如果只是从观众进场那个时点开始想，这其实就是被固化经验所限制了。就好比你做一个产品之前，只是想发布会怎么开，宣传文案怎么写，然后再倒回来想产品应该怎么做。

但是我必须告诉朋友们，我现在不这么看了。我认为不应该从例如12月30号产品发布那个节点开始想，那是我10年前的思路了，那时候是从用户体验出发。当然，今天你让我再画思维导图的话，我肯定会从1月2号或3号，用户在电梯里跟别人分享这个跨年演讲的那个瞬间开始想。

也就是说，只有当你的用户跟他的家人、他的朋友、他的同事去讲这个项目、这个产品的时候——前两天某位老师来直播，据说有98%的观众也去看了另一位大咖的直播，我合理推断他可能是个公务员。如果我真的要服务这98%的观众，我得让他写文件的时候也能用上（从演讲中听到的）一两句。

你从用户使用你产品的那个瞬间反推，一场直播，你能不能设计出来100个关键词？不可能。最多有两三个核心观点，对不对？这才是直播合理的结构。你回到用户的应用场景里，你马上就知道一场直播怎么设计才是好的。

这是我的第一个经验：如果是复杂项目，一定要倒推其难点和关键环节，千万不要正着做计划。你正着做计划，往往会出两个问题：
1. 你会发现什么事都想干，好像没有一件事不该干的，但是你没有那么多资源，也不可能做那么多事情。
2. 你一定会漏掉最重要的事，因为做最重要的事往往超出了你的现有经验。

那你就倒过来想，这件事才有可能成立。

### 建议二：出门找标杆，不要闭门造车

第二个建议呢，就是要出门找标杆学习，不要闭门造车搞研发。我们对搞研发，很容易陷入误区，以为自己做的就是创新。不是这样的，搞研发最重要的是找准方向和对标。

任正非说过很多次：你会陷入在一个幻想里面，以为你是全世界最创新的人，但是你那百分之九十九点九的所谓创新，这个世界上可能早就有人已经干完了，人家连专利都申请了，你还在这儿以为自己发现了个新课题呢。

所以呢，如果我们要做一个复杂项目，一定要出门去找参考，找标杆。否则就会给自己找很多借口，比如："花姐，我也不像你人脉那么多，我认识的人有限。"

这件事情是一个"顺藤摸瓜"的过程：你顺着一个藤，藤再小，往上摸，肯定能摸着瓜。而今天你要总结哪个人的经验？比如说，关于饺子，你说："我没找到做饺子最好的人，我就不行。"那饺子的外包服务商行不行？给某个知名饺子品牌做过宣发的人行不行？找个行业顾问，每个顾问都能给你一些启发吧。

在这个过程当中，其实你就形成了自己的经验。等你真的摸到主线的时候，你的方案也是可行的。

我们经常看到同事说："我们开个会，开个启动会。"方案文档一打开，好多页的文档，这样的文档我连看都懒得看，直接关掉。以我的经验来判断的话，这种方案多半是落不了地的。为什么？因为你用形式上的复杂来掩盖你不知道这事儿到底该怎么干的那个真相。

怎么做出一个完善的方案？我们同事工作中，经常说给领导提方案，一遍一遍又一遍，领导每次都有新意见。这往往是因为你没有从一开始就统一核心目标和故事线。你开始把这个目标统一了，后面的事情就会非常快。

第二，你要明确关键点控制，最重要的是三个东西：
1. 抓大放小。
2. 最主要的三个子目标或者说三条清晰的主线是什么？把这三条先放上。
3. 第三条叫做基本的学习路径，你先告诉领导，你是从哪儿学来的，你的方法论基础是什么。

你把这三条先说明白了，实际上你内部的思想统一就非常容易实现了。比如你是从华为学来的，你在内部的说服力，和你自己瞎琢磨出来的经验，在内部的说服力是完全不一样的。

我怎么让自己团队的目标和思路清晰呢？其实这是我们的工作方法所决定的。

### 建议三：尽早启动，不要等万事俱备

第三个建议叫做要尽早启动，快速行动，不要等万事俱备。啥意思呀？所有的复杂的大项目，越想，这事儿就越觉得不可做。你自己想建功立业，你也不能让你的领导因为反复犹豫而错失良机。越想人越复杂，越想条件越不具备，然后就干脆算了。

对于我们来说，最好的策略就是赶紧干起来，一旦启动就不能再往回退了。事情就要干起来，干着干着，资源就有了，条件就具备了。干不好又能坏到哪里去呢？所以还是我那句话："能跑多快跑多快，这公司是老板的又不是你的，你怕啥呢？"对不对？

对于你最大的价值就是经验的积累，所以一定要推动这个事赶快执行。越早启动，对执行者越有利。这件事情，在方案阶段大家给意见的那个节点，和这件事情已经变成了一个正在执行的项目，大家给意见的时候，那个态度是完全不一样的。

我老是批评那些总想着"万事俱备，只欠东风"的人。这个世界上本来就是万事俱备的，世界准备好了成就一切人。差的是什么呢？欠的是你这个人是否自带东风。你自己就是东风阵阵，你就会发现这个世界的条件都是为你准备好的。你自己如果从来就觉得："我没有安全感，你们都没有给我准备好，我条件不具备。"那你啥事也干不成。

## 结语

所以我送大家一个忠告叫做："你就是你自己的东风。"就真的是这样的，你等，什么都不会等来，那只能就是永远没成果。

对一个复杂项目来说，非常非常关键的不是这个公司的条件，而是说一个好的项目经理，他一定要能乘着那股东风，他一定要有想做一个不一样的事的那股劲头，要借力、要起那个劲。我觉得这是让一个项目能成的那个第一推动力。