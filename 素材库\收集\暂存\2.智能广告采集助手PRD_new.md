# 智能广告采集助手PRD

## 1. 产品概述
### 1.1 背景和机会点
- 用户在日常浏览各类APP时经常遇到优质广告
- 现有的手动截图方式存在信息不完整、管理混乱的问题
- 市场上缺乏专门的广告采集和管理工具
- 对于创业者和营销人员来说，优质广告案例是重要的参考资源

### 1.2 产品定位
自动化的广告采集和管理系统，帮助用户高效收集、分类和管理各类广告素材的专业工具。

### 1.3 目标用户画像
- 创业者：需要收集行业案例和营销灵感
- 营销人员：需要研究竞品广告策略
- 运营人员：需要跟踪行业动态和趋势
- 内容创作者：需要收集优质文案和创意

### 1.4 竞品分析
- 现有截图工具的优缺点
- 广告监控工具的对比
- 素材管理工具的分析

### 1.5 产品核心价值
- 自动化采集，节省手动操作时间
- 完整保存广告信息，避免信息缺失
- 结构化管理，提高素材复用效率
- 智能分析，发现营销趋势和机会

## 2. 功能需求
### 2.1 功能地图
- 自动采集系统
- 内容管理系统
- 智能分析系统
- 数据存储系统

### 2.2 核心功能（按优先级排序）
#### P0：必须实现的核心功能
- 自动广告识别和采集
- 广告内容完整存储
- 基础分类和标签管理
- 搜索和筛选功能

#### P1：重要但非必须功能
- 智能分类推荐
- 广告文案提取
- 数据统计分析

#### P2：nice to have的功能
- 广告趋势分析
- 竞品投放分析

## 3. 详细功能说明

### 3.1 自动采集功能
#### 功能描述
自动监控用户授权的APP，识别并采集广告内容。

#### 广告识别规则
1. 关键标识识别
   - 检测页面四个角落的"广告"标识
   - 识别常见广告标记：广告/推广/赞助/Ad
   - 支持中英文标识识别

2. 广告区域定位
   - 定位广告标识位置
   - 向内扩展捕获完整广告内容区域
   - 智能识别广告边界

3. 广告类型判断
   - 信息流广告
   - 搜索结果广告
   - 弹窗广告
   - 横幅广告


#### 用户故事
作为一个营销人员，我希望软件能自动保存我浏览时看到的广告，这样我就不用手动截图了。

#### 交互流程
1. 自动打开APP
2. 识别到广告内容时自动采集
3. 通知用户新的采集结果

#### 技术依赖
- 安卓无障碍服务
- 广告识别算法
- 后端和安卓端长链接

### 3.2 内容管理功能
#### 功能描述
提供广告内容的分类、标签、搜索和管理功能，帮助用户高效组织和查找已采集的广告素材。

#### 用户故事
作为一个创业者，我希望能够快速找到之前保存的广告案例，通过多种方式筛选和查看，方便我参考和学习。

#### 交互流程
1. 广告列表展示
   - 瀑布流展示广告缩略图
   - 支持按时间、来源、类型等排序
   - 支持网格视图和列表视图切换

2. 广告详情查看
   - 显示完整广告图片/视频
   - 展示广告文案内容
   - 显示来源APP和采集时间
   - 提供编辑标签和备注功能

3. 分类管理
   - 支持创建自定义分类
   - 拖拽式分类调整
   - 快速分类标记

4. 搜索功能
   - 支持关键词搜索
   - 支持标签筛选
   - 支持时间范围筛选
   - 支持来源APP筛选



#### 数据结构
1. 广告内容表
   - ID：唯一标识
   - 标题：广告标题
   - 内容：广告文案
   - 图片/视频：素材文件
   - 来源：来源APP
   - 采集时间：时间戳
   - 分类ID：所属分类
   - 标签：标签列表
   - 备注：用户备注

2. 分类表
   - ID：分类ID
   - 名称：分类名称
   - 排序：显示顺序
   - 创建时间：时间戳

3. 标签表
   - ID：标签ID
   - 名称：标签名称
   - 使用次数：统计数据

#### 界面原型
1. 主列表页
   ```
   +----------------+
   | 搜索栏         |
   +----------------+
   | 筛选条件       |
   +----------------+
   | 广告内容列表   |
   |  +---+ +---+  |
   |  |Ad | |Ad |  |
   |  +---+ +---+  |
   +----------------+
   ```

2. 详情页
   ```
   +----------------+
   | 广告内容      |
   +----------------+
   | 文案/描述     |
   +----------------+
   | 标签管理      |
   +----------------+
   | 备注区域      |
   +----------------+
   ```

### 3.3 用户系统功能
#### 功能描述
提供用户注册、登录、设备管理等SAAS化功能，支持多用户数据隔离。

#### 用户故事
作为一个企业用户，我希望能够管理多个采集设备，并在后台查看所有设备采集的广告数据。

#### 核心功能
1. 用户管理
   - 用户注册与登录

2. 设备管理
   - 设备绑定（6位数设备码）
   - 设备状态监控
   - 设备采集配置
   - 设备解绑功能

3. 数据隔离
   - 用户数据独立存储
   - 设备数据关联用户
   - 多设备数据汇总


#### 数据结构
1. 用户表
   - ID：用户唯一标识
   - 用户名：登录账号
   - 密码：加密存储
   - 创建时间：注册时间
   - 会员等级：用户等级
   - 状态：账号状态

2. 设备表
   - ID：设备唯一标识
   - 设备码：6位绑定码
   - 用户ID：所属用户
   - 设备名称：自定义名称
   - 绑定时间：首次绑定时间
   - 最后在线：最后活跃时间
   - 状态：在线状态

#### 界面原型
1. 设备管理页
   ```
   +----------------+
   | 设备列表       |
   +----------------+
   | 设备码 状态    |
   | #123456 在线   |
   | #789012 离线   |
   +----------------+
   | + 绑定新设备   |
   +----------------+
   ```

2. 设备绑定弹窗
   ```
   +----------------+
   | 绑定新设备     |
   +----------------+
   | 设备码：______ |
   | 名称：________ |
   +----------------+
   | [确认] [取消]  |
   +----------------+
   ```

### 3.4 Android采集端
#### 功能描述
基于Android无障碍服务开发的广告采集APP，支持自动采集和设备绑定。

#### 核心功能
1. 设备初始化
   - 生成唯一设备码
   - 生成设备绑定6位绑定码
   - 等待用户绑定

2. 无障碍服务
   - 权限申请引导
   - 后台服务管理
   - 自动采集控制

3. 数据同步
   - 实时上传采集数据
   - 定时同步状态
   - 断线重连机制

