<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>出海三件套最后一环：为什么IP这么复杂？</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f5f7fa;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 30px;
            text-align: center;
        }
        
        .main-title {
            font-size: 42px;
            font-weight: bold;
            margin-bottom: 20px;
        }
        
        .subtitle {
            font-size: 20px;
            opacity: 0.9;
        }
        
        .content {
            padding: 0;
        }
        
        .section {
            margin-bottom: 0;
            border-bottom: 1px solid #eee;
        }
        
        .text-section {
            padding: 60px 40px;
            max-width: 900px;
            margin: 0 auto;
        }
        
        .chart-section {
            padding: 0;
            background: #f8f9fa;
        }
        
        .section-title {
            font-size: 32px;
            color: #2c3e50;
            margin-bottom: 30px;
            font-weight: bold;
            text-align: center;
        }
        
        .section-subtitle {
            font-size: 24px;
            color: #34495e;
            margin: 30px 0 20px 0;
            font-weight: bold;
        }
        
        .paragraph {
            font-size: 18px;
            line-height: 1.8;
            margin-bottom: 25px;
            color: #444;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 3px 8px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .quote {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            margin: 30px 0;
            border-radius: 10px;
            font-size: 20px;
            text-align: center;
            font-weight: bold;
        }
        
        .chart-container {
            width: 100%;
            height: 100vh;
            min-height: 600px;
            position: relative;
        }
        
        .chart-iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        .chart-nav {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 100;
        }
        
        .chart-nav button {
            background: rgba(255,255,255,0.9);
            border: none;
            padding: 10px 15px;
            margin-left: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .chart-nav button:hover {
            background: white;
            transform: translateY(-2px);
        }
        
        .list {
            margin: 25px 0;
            padding-left: 0;
            list-style: none;
        }
        
        .list-item {
            margin-bottom: 15px;
            font-size: 18px;
            line-height: 1.6;
            padding: 15px 20px;
            background: #f8f9fa;
            border-left: 4px solid #3498db;
            border-radius: 5px;
        }
        
        .emphasis {
            font-weight: bold;
            color: #2c3e50;
        }
        
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 30px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            margin: 30px 0;
            overflow-x: auto;
            white-space: pre;
            font-size: 16px;
            line-height: 1.4;
        }
        
        .nav-dots {
            position: fixed;
            right: 30px;
            top: 50%;
            transform: translateY(-50%);
            z-index: 1000;
        }
        
        .nav-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(102, 126, 234, 0.3);
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .nav-dot.active {
            background: #667eea;
            transform: scale(1.3);
        }
        
        .footer {
            background: #2c3e50;
            color: white;
            padding: 50px 40px;
            text-align: center;
        }
        
        .footer-title {
            font-size: 24px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        
        .footer-content {
            max-width: 800px;
            margin: 0 auto;
            font-size: 16px;
            line-height: 1.8;
        }
        
        @media (max-width: 768px) {
            .main-title {
                font-size: 28px;
            }
            
            .text-section {
                padding: 40px 20px;
            }
            
            .section-title {
                font-size: 24px;
            }
            
            .chart-container {
                height: 70vh;
                min-height: 400px;
            }
            
            .nav-dots {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1 class="main-title">出海三件套最后一环</h1>
            <p class="subtitle">为什么IP这么复杂？</p>
        </header>
        
        <main class="content">
            <!-- 开篇 -->
            <section class="section text-section" id="section-0">
                <h2 class="section-title">开篇</h2>
                <p class="paragraph">出海三件套：手机、卡、IP。</p>
                <p class="paragraph">水最深的就是IP了吧。</p>
                <p class="paragraph">0播放了，掉橱窗了，什么问题都可以让IP背下锅。同时市面上又有各种各样的解决方案：小火箭、软路由、SDWAN、专线、机房IP、住宅IP...</p>
                <p class="paragraph">价格也是个谜，从一个月10几块到一个月1000-2000。价格跨度巨大，对于小白来说，肉眼看起来好像又没有什么区别。</p>
                <div class="quote">今天我就从10年前网游加速器的经历说起，彻底讲清楚IP这件事的本质。</div>
            </section>
            
            <!-- 3个问题框架图 -->
            <section class="section chart-section" id="section-1">
                <div class="chart-container">
                    <iframe src="../image/01-三个问题框架图.html" class="chart-iframe"></iframe>
                    <div class="chart-nav">
                        <button onclick="openChart('../image/01-三个问题框架图.html')">全屏查看</button>
                    </div>
                </div>
            </section>
            
            <!-- 网游加速器历史 -->
            <section class="section text-section" id="section-2">
                <h2 class="section-title">网游加速器的那些年</h2>
                <p class="paragraph">2014-2015年，我加入了一家网游加速器公司，当时是TOP3的加速器。我负责搞流量。</p>
                <p class="paragraph">网游加速器这个产品是怎么来的？就不得不提英雄联盟和暗黑破坏神3这两个标志性的游戏了。</p>
                
                <h3 class="section-subtitle">英雄联盟的诱惑</h3>
                <p class="paragraph">2014-2015年，韩服（KR）是公认的LOL"圣地"，职业选手和顶尖路人云集。去韩服打Rank是证明自己、提升技术的最佳途径。</p>
                
                <h3 class="section-subtitle">暗黑破坏神3的无奈</h3>
                <p class="paragraph">《暗黑破坏神3》全球发售是2012年，但国服直到2015年4月才正式公测。在这之前，中国玩家想玩这款大作，唯一途径就是去玩亚服、美服或欧服。</p>
            </section>
            
            <!-- 网络架构对比图 -->
            <section class="section chart-section" id="section-3">
                <div class="chart-container">
                    <iframe src="../image/02-网络架构对比图.html" class="chart-iframe"></iframe>
                    <div class="chart-nav">
                        <button onclick="openChart('../image/02-网络架构对比图.html')">全屏查看</button>
                    </div>
                </div>
            </section>
            
            <!-- 场景对比 -->
            <section class="section text-section" id="section-4">
                <h2 class="section-title">游戏场景 vs 营销场景</h2>
                <p class="paragraph">理解了这3个问题，我们再来看不同场景的需求差异：</p>
                <p class="paragraph">在营销场景中，其实简化成了<span class="highlight">2个核心问题</span>：</p>
                <ul class="list">
                    <li class="list-item"><span class="emphasis">IP地理限制</span> - 不能登录TikTok、Facebook等</li>
                    <li class="list-item"><span class="emphasis">IP质量问题</span> - 会被TK 0播放、封号</li>
                </ul>
                <p class="paragraph">为什么可以简化？因为营销场景对网络延迟的容忍度很高，200ms的延迟完全不影响刷视频、发帖等操作。</p>
            </section>
            
            <!-- 场景需求对比表 -->
            <section class="section chart-section" id="section-5">
                <div class="chart-container">
                    <iframe src="../image/03-场景需求对比表.html" class="chart-iframe"></iframe>
                    <div class="chart-nav">
                        <button onclick="openChart('../image/03-场景需求对比表.html')">全屏查看</button>
                    </div>
                </div>
            </section>
            
            <!-- 营销场景问题简化 -->
            <section class="section chart-section" id="section-6">
                <div class="chart-container">
                    <iframe src="../image/07-营销场景问题简化图.html" class="chart-iframe"></iframe>
                    <div class="chart-nav">
                        <button onclick="openChart('../image/07-营销场景问题简化图.html')">全屏查看</button>
                    </div>
                </div>
            </section>
            
            <!-- 1000台服务器问题 -->
            <section class="section text-section" id="section-7">
                <h2 class="section-title">1000个TK账号，需要买1000台服务器吗？</h2>
                <p class="paragraph">一个服务器是把CPU、内存、硬盘、流量打包在一起出售的。你想要多个IP就需要买多个服务器。</p>
                <p class="paragraph">聪明的你是不是在想：买1000台服务器实在是太贵了。你只想要1000个IP，其他的CPU、内存、硬盘买一台服务器就行了。</p>
                <div class="quote">答案是可以的。</div>
            </section>
            
            <!-- 1000台服务器问题图 -->
            <section class="section chart-section" id="section-8">
                <div class="chart-container">
                    <iframe src="../image/08-1000台服务器问题图.html" class="chart-iframe"></iframe>
                    <div class="chart-nav">
                        <button onclick="openChart('../image/08-1000台服务器问题图.html')">全屏查看</button>
                    </div>
                </div>
            </section>
            
            <!-- 代理服务商 -->
            <section class="section text-section" id="section-9">
                <h2 class="section-title">代理IP服务商</h2>
                <p class="paragraph">922S5proxy、ipipgo、cliproxy等等，这些都是专业的代理网络服务商。</p>
                <p class="paragraph">比如美国的一个家庭住宅独立IP，一个月大概30元。</p>
                <p class="paragraph">既然有这样的代理服务商，提供了美国的住宅IP，不能直接用这个IP吗？</p>
                <p class="paragraph">不能。仔细看，这些代理IP服务商都会提示说<span class="highlight">"不接受中国IP直连"</span>。至于为什么，你猜。</p>
            </section>
            
            <!-- 代理服务商限制图 -->
            <section class="section chart-section" id="section-10">
                <div class="chart-container">
                    <iframe src="../image/09-代理服务商限制图.html" class="chart-iframe"></iframe>
                    <div class="chart-nav">
                        <button onclick="openChart('../image/09-代理服务商限制图.html')">全屏查看</button>
                    </div>
                </div>
            </section>
            
            <!-- 完整解决方案 -->
            <section class="section text-section" id="section-11">
                <h2 class="section-title">完整解决方案</h2>
                <p class="paragraph">还记得上面说的网游加速器的第二个问题——网络速度吗？通过网络中转来解决了速度问题。</p>
                <p class="paragraph">这里同样可以用网络中转来解决代理IP服务商不能直接使用的问题。</p>
                <div class="code-block">中国用户 ——> 海外中转 ——> 代理服务商 ——> 目标平台
   🇨🇳         🇭🇰          🇺🇸           🇺🇸</div>
                <p class="paragraph">通过链式代理配置，就能实现一台手机一个独立IP。</p>
            </section>
            
            <!-- 完整链式代理方案图 -->
            <section class="section chart-section" id="section-12">
                <div class="chart-container">
                    <iframe src="../image/10-完整链式代理方案图.html" class="chart-iframe"></iframe>
                    <div class="chart-nav">
                        <button onclick="openChart('../image/10-完整链式代理方案图.html')">全屏查看</button>
                    </div>
                </div>
            </section>
        </main>
        
        <footer class="footer">
            <h3 class="footer-title">核心洞察</h3>
            <div class="footer-content">
                <p>链式代理的真正价值不是技术复杂化，而是<strong>资源解耦带来的成本优化</strong>。</p>
                <p>理解了这个本质，你就能根据自己的需求选择合适的方案，不会被各种营销话术迷惑。</p>
            </div>
        </footer>
    </div>
    
    <!-- 导航点 -->
    <div class="nav-dots" id="navDots"></div>
    
    <script>
        // 创建导航点
        function createNavDots() {
            const sections = document.querySelectorAll('.section');
            const navDots = document.getElementById('navDots');
            
            sections.forEach((section, index) => {
                const dot = document.createElement('div');
                dot.className = 'nav-dot';
                dot.addEventListener('click', () => {
                    section.scrollIntoView({ behavior: 'smooth' });
                });
                navDots.appendChild(dot);
            });
        }
        
        // 更新活跃导航点
        function updateActiveDot() {
            const sections = document.querySelectorAll('.section');
            const dots = document.querySelectorAll('.nav-dot');
            
            let activeIndex = 0;
            sections.forEach((section, index) => {
                const rect = section.getBoundingClientRect();
                if (rect.top <= window.innerHeight / 2 && rect.bottom >= window.innerHeight / 2) {
                    activeIndex = index;
                }
            });
            
            dots.forEach((dot, index) => {
                dot.classList.toggle('active', index === activeIndex);
            });
        }
        
        // 全屏查看图表
        function openChart(url) {
            window.open(url, '_blank');
        }
        
        // 初始化
        window.addEventListener('load', function() {
            createNavDots();
            updateActiveDot();
        });
        
        window.addEventListener('scroll', updateActiveDot);
    </script>
</body>
</html>
