<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>传统团队角色局限性</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .glass-card {
            background: rgba(255,255,255,0.15);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255,255,255,0.3);
        }
        
        .role-card {
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .role-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.3);
            border-color: rgba(255,255,255,0.5);
        }
        
        .role-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.5s;
        }
        
        .role-card:hover::before {
            left: 100%;
        }
        
        .avatar {
            background: rgba(255,255,255,0.2);
            backdrop-filter: blur(5px);
        }
        
        .limitation-tag {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255,255,255,0.2);
        }
    </style>
</head>
<body class="gradient-bg">
    <div class="container mx-auto px-6 py-12">
        <!-- 标题 -->
        <div class="text-center mb-16">
            <h1 class="text-5xl font-bold text-white mb-6">
                互联网公司的困境
            </h1>
            <h2 class="text-3xl font-semibold text-white/90 mb-4">
                我们都是生产线上的螺丝钉
            </h2>
            <p class="text-xl text-white/80 max-w-3xl mx-auto">
                身怀一技之长，但又都被自己的技能边界死死地限制住
            </p>
        </div>
        
        <!-- 四个角色卡片 -->
        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-7xl mx-auto mb-12">
            <!-- 产品经理 -->
            <div class="role-card glass-card rounded-2xl p-6 text-center">
                <div class="avatar w-20 h-20 rounded-full mx-auto mb-4 flex items-center justify-center">
                    <span class="text-4xl">👨‍💼</span>
                </div>
                <h3 class="text-xl font-bold text-white mb-3">产品经理</h3>
                <div class="text-white/90 text-sm mb-4">
                    <p class="mb-2">✅ 满脑子产品想法</p>
                    <p class="mb-2">✅ 用户需求分析一套一套</p>
                </div>
                <div class="limitation-tag rounded-lg p-3 mb-3">
                    <p class="text-red-200 font-semibold text-sm">❌ 只会画原型图</p>
                    <p class="text-red-200 text-xs mt-1">然后满世界"找资源"</p>
                </div>
                <div class="text-white/70 text-xs italic">
                    "这个功能很简单，就是..."
                </div>
            </div>
            
            <!-- 前端工程师 -->
            <div class="role-card glass-card rounded-2xl p-6 text-center">
                <div class="avatar w-20 h-20 rounded-full mx-auto mb-4 flex items-center justify-center">
                    <span class="text-4xl">👨‍💻</span>
                </div>
                <h3 class="text-xl font-bold text-white mb-3">前端工程师</h3>
                <div class="text-white/90 text-sm mb-4">
                    <p class="mb-2">✅ 做页面真牛逼</p>
                    <p class="mb-2">✅ 动效交互都能整</p>
                </div>
                <div class="limitation-tag rounded-lg p-3 mb-3">
                    <p class="text-red-200 font-semibold text-sm">❌ 后台？懵了</p>
                    <p class="text-red-200 text-xs mt-1">眼巴巴等后端"投喂"</p>
                </div>
                <div class="text-white/70 text-xs italic">
                    "接口什么时候给我？"
                </div>
            </div>
            
            <!-- 后端工程师 -->
            <div class="role-card glass-card rounded-2xl p-6 text-center">
                <div class="avatar w-20 h-20 rounded-full mx-auto mb-4 flex items-center justify-center">
                    <span class="text-4xl">👨‍🔧</span>
                </div>
                <h3 class="text-xl font-bold text-white mb-3">后端工程师</h3>
                <div class="text-white/90 text-sm mb-4">
                    <p class="mb-2">✅ 写代码那叫一个6</p>
                    <p class="mb-2">✅ 数据库接口门儿清</p>
                </div>
                <div class="limitation-tag rounded-lg p-3 mb-3">
                    <p class="text-red-200 font-semibold text-sm">❌ 前端界面丑哭</p>
                    <p class="text-red-200 text-xs mt-1">连自己都看不下去</p>
                </div>
                <div class="text-white/70 text-xs italic">
                    "能用就行，别要求太高"
                </div>
            </div>
            
            <!-- UI设计师 -->
            <div class="role-card glass-card rounded-2xl p-6 text-center">
                <div class="avatar w-20 h-20 rounded-full mx-auto mb-4 flex items-center justify-center">
                    <span class="text-4xl">👨‍🎨</span>
                </div>
                <h3 class="text-xl font-bold text-white mb-3">UI设计师</h3>
                <div class="text-white/90 text-sm mb-4">
                    <p class="mb-2">✅ 设计图做得漂亮</p>
                    <p class="mb-2">✅ 普通需求整得高大上</p>
                </div>
                <div class="limitation-tag rounded-lg p-3 mb-3">
                    <p class="text-red-200 font-semibold text-sm">❌ 技术实现？傻眼</p>
                    <p class="text-red-200 text-xs mt-1">"诶？这个能做出来吗？"</p>
                </div>
                <div class="text-white/70 text-xs italic">
                    "我觉得这样更好看..."
                </div>
            </div>
        </div>
        
        <!-- 核心问题总结 -->
        <div class="text-center">
            <div class="glass-card rounded-2xl p-8 max-w-4xl mx-auto">
                <h3 class="text-3xl font-bold text-white mb-6">
                    💔 核心困境
                </h3>
                <p class="text-xl text-white/90 leading-relaxed mb-6">
                    每个人都想过搞一个完整的产品<br>
                    但现实是，想一想还是继续搬砖
                </p>
                <div class="bg-white/10 rounded-xl p-6">
                    <p class="text-2xl font-bold text-yellow-200 mb-2">
                        🔒 技能边界的牢笼
                    </p>
                    <p class="text-white/80">
                        我们都身怀一技之长，但又都被自己的技能边界死死地限制住
                    </p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
