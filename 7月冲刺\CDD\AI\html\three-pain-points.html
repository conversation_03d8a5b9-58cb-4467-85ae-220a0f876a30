<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>出海矩阵三大痛点</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .glass-card {
            background: rgba(255,255,255,0.15);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255,255,255,0.3);
        }
        
        .pain-point-card {
            transition: all 0.3s ease;
        }
        
        .pain-point-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            border-color: rgba(255,255,255,0.5);
        }
        
        .icon-container {
            background: rgba(255,255,255,0.2);
            backdrop-filter: blur(5px);
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        .floating {
            animation: float 3s ease-in-out infinite;
        }
        
        .floating:nth-child(2) {
            animation-delay: 1s;
        }
        
        .floating:nth-child(3) {
            animation-delay: 2s;
        }
    </style>
</head>
<body class="gradient-bg">
    <div class="container mx-auto px-6 py-12">
        <!-- 标题 -->
        <div class="text-center mb-16">
            <h1 class="text-5xl font-bold text-white mb-6">
                出海矩阵业务
            </h1>
            <h2 class="text-3xl font-semibold text-white/90 mb-4">
                三大拦路虎
            </h2>
            <p class="text-xl text-white/80 max-w-2xl mx-auto">
                这三样东西搞不定，出海业务就没办法做
            </p>
        </div>
        
        <!-- 三大痛点卡片 -->
        <div class="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            <!-- 手机 -->
            <div class="pain-point-card glass-card rounded-3xl p-8 text-center floating">
                <div class="icon-container w-24 h-24 rounded-full mx-auto mb-6 flex items-center justify-center">
                    <span class="text-5xl">📱</span>
                </div>
                <h3 class="text-2xl font-bold text-white mb-4">手机</h3>
                <div class="text-white/90 space-y-2">
                    <p class="text-lg">• 海外账号注册</p>
                    <p class="text-lg">• 设备指纹管理</p>
                    <p class="text-lg">• 批量操作需求</p>
                    <p class="text-lg">• 风控规避</p>
                </div>
                <div class="mt-6 px-4 py-2 bg-red-500/20 rounded-full">
                    <span class="text-red-200 font-semibold">硬件成本高</span>
                </div>
            </div>
            
            <!-- SIM卡 -->
            <div class="pain-point-card glass-card rounded-3xl p-8 text-center floating">
                <div class="icon-container w-24 h-24 rounded-full mx-auto mb-6 flex items-center justify-center">
                    <span class="text-5xl">📶</span>
                </div>
                <h3 class="text-2xl font-bold text-white mb-4">SIM卡</h3>
                <div class="text-white/90 space-y-2">
                    <p class="text-lg">• 海外手机号码</p>
                    <p class="text-lg">• 验证码接收</p>
                    <p class="text-lg">• 多国家覆盖</p>
                    <p class="text-lg">• 长期稳定性</p>
                </div>
                <div class="mt-6 px-4 py-2 bg-orange-500/20 rounded-full">
                    <span class="text-orange-200 font-semibold">渠道复杂</span>
                </div>
            </div>
            
            <!-- IP -->
            <div class="pain-point-card glass-card rounded-3xl p-8 text-center floating">
                <div class="icon-container w-24 h-24 rounded-full mx-auto mb-6 flex items-center justify-center">
                    <span class="text-5xl">🌐</span>
                </div>
                <h3 class="text-2xl font-bold text-white mb-4">IP地址</h3>
                <div class="text-white/90 space-y-2">
                    <p class="text-lg">• 地理位置匹配</p>
                    <p class="text-lg">• 代理服务质量</p>
                    <p class="text-lg">• 多IP轮换</p>
                    <p class="text-lg">• 反检测能力</p>
                </div>
                <div class="mt-6 px-4 py-2 bg-blue-500/20 rounded-full">
                    <span class="text-blue-200 font-semibold">技术门槛</span>
                </div>
            </div>
        </div>
        
        <!-- 底部总结 -->
        <div class="text-center mt-16">
            <div class="glass-card rounded-2xl p-8 max-w-4xl mx-auto">
                <h3 class="text-2xl font-bold text-white mb-4">
                    💡 核心问题
                </h3>
                <p class="text-xl text-white/90 leading-relaxed">
                    每一个痛点都是硬邦邦的技术和成本门槛<br>
                    解决了这三个问题，就解决了所有想做海外矩阵业务的人的需求
                </p>
            </div>
        </div>
    </div>
</body>
</html>
