<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络架构对比：直连 vs 链式代理</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .title {
            text-align: center;
            font-size: 28px;
            color: #2c3e50;
            margin-bottom: 40px;
            font-weight: bold;
        }
        
        .comparison-container {
            display: flex;
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .comparison-section {
            flex: 1;
            border-radius: 12px;
            padding: 25px;
            position: relative;
        }
        
        .direct-section {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
        }
        
        .proxy-section {
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
            color: white;
        }
        
        .section-title {
            font-size: 22px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .network-diagram {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .node {
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.5);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            min-width: 80px;
            font-weight: bold;
        }
        
        .arrow {
            font-size: 20px;
            font-weight: bold;
        }
        
        .stats {
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        
        .stat-label {
            font-weight: bold;
        }
        
        .stat-value {
            font-family: monospace;
        }
        
        .advantages {
            margin-top: 40px;
        }
        
        .advantages-title {
            font-size: 20px;
            color: #2c3e50;
            text-align: center;
            margin-bottom: 20px;
            font-weight: bold;
        }
        
        .advantages-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .advantage-box {
            background: #f8f9fa;
            border-left: 4px solid #3498db;
            padding: 20px;
            border-radius: 8px;
        }
        
        .advantage-title {
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .advantage-desc {
            color: #7f8c8d;
            font-size: 14px;
        }
        
        .technical-details {
            margin-top: 40px;
            background: #ecf0f1;
            border-radius: 10px;
            padding: 25px;
        }
        
        .technical-title {
            font-size: 18px;
            color: #2c3e50;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .technical-list {
            list-style: none;
            padding: 0;
        }
        
        .technical-list li {
            background: white;
            margin-bottom: 10px;
            padding: 12px 15px;
            border-radius: 6px;
            border-left: 3px solid #3498db;
        }
        
        .flag {
            font-size: 18px;
            margin-left: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="title">网络架构对比：直连 vs 链式代理</div>
        
        <div class="comparison-container">
            <div class="comparison-section direct-section">
                <div class="section-title">❌ 直连方案</div>
                
                <div class="network-diagram">
                    <div class="node">
                        你的设备<br>
                        <span class="flag">🇨🇳</span>
                    </div>
                    <div class="arrow">────────────────→</div>
                    <div class="node">
                        美国服务器<br>
                        <span class="flag">🇺🇸</span>
                    </div>
                </div>
                
                <div class="stats">
                    <div class="stat-item">
                        <span class="stat-label">延迟:</span>
                        <span class="stat-value">300ms+</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">丢包率:</span>
                        <span class="stat-value">5-15%</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">稳定性:</span>
                        <span class="stat-value">差</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">成本:</span>
                        <span class="stat-value">$10/月</span>
                    </div>
                </div>
            </div>
            
            <div class="comparison-section proxy-section">
                <div class="section-title">✅ 链式代理</div>
                
                <div class="network-diagram">
                    <div class="node">
                        你的设备<br>
                        <span class="flag">🇨🇳</span>
                    </div>
                    <div class="arrow">→</div>
                    <div class="node">
                        香港中转<br>
                        <span class="flag">🇭🇰</span>
                    </div>
                    <div class="arrow">→</div>
                    <div class="node">
                        美国落地<br>
                        <span class="flag">🇺🇸</span>
                    </div>
                </div>
                
                <div class="stats">
                    <div class="stat-item">
                        <span class="stat-label">延迟:</span>
                        <span class="stat-value">170ms</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">丢包率:</span>
                        <span class="stat-value">< 1%</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">稳定性:</span>
                        <span class="stat-value">优秀</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">成本:</span>
                        <span class="stat-value">$30/月</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="advantages">
            <div class="advantages-title">链式代理的技术优势</div>
            
            <div class="advantages-grid">
                <div class="advantage-box">
                    <div class="advantage-title">🚀 路径优化</div>
                    <div class="advantage-desc">选择最优网络路径，避开拥堵节点，显著降低延迟</div>
                </div>
                
                <div class="advantage-box">
                    <div class="advantage-title">🛡️ 稳定性提升</div>
                    <div class="advantage-desc">多重备份路径，单点故障不影响整体连接</div>
                </div>
                
                <div class="advantage-box">
                    <div class="advantage-title">🌐 地理优势</div>
                    <div class="advantage-desc">利用香港到内地的高速连接，再转美国专线</div>
                </div>
                
                <div class="advantage-box">
                    <div class="advantage-title">⚡ 协议优化</div>
                    <div class="advantage-desc">使用专业代理协议，减少握手时间和数据包开销</div>
                </div>
            </div>
        </div>
        
        <div class="technical-details">
            <div class="technical-title">🔧 技术实现细节</div>
            <ul class="technical-list">
                <li><strong>第一跳 (中国→香港):</strong> 50ms延迟，利用地理优势和优质线路</li>
                <li><strong>第二跳 (香港→美国):</strong> 100ms延迟，使用海底光缆专线</li>
                <li><strong>第三跳 (美国中转→目标):</strong> 20ms延迟，本地高速网络</li>
                <li><strong>协议优化:</strong> 使用SOCKS5/VMess等高效代理协议</li>
                <li><strong>负载均衡:</strong> 智能选择最优节点，动态切换路径</li>
            </ul>
        </div>
    </div>
</body>
</html>
