# 基本功5：算模型|单元模型

我们常常说，商业的本质是：赚钱，持续赚钱，持续赚大钱，其实我们判断商业模式成立的核心，就是判断3个关键问题：

1. 能不能赚钱？这是在判断商业模式的营利性。
2. 能不能持续赚钱？这是在判断商业模式的可复制性。
3. 能不能持续赚大钱？这是在判断商业模式的长期壁垒。

关于商业模式，市场上其实有很多分析工具，比如UE（Unit Economics），比如财务预测模型。但是这些工具对于早期的创业项目并不太友好。

经过我们的教研团队和以太的投资团队"大望资本"探讨了上百个小时，最终我们总结出一个适合早期项目的评估模型，我们叫它"单元模型"。

单元模型就是你的业务运营的一个最小单元，是业务未来持续复制的基础。在创业早期，算账的核心，都应该围绕单元模型来做。它可以帮助早期创业者，在没有那么多财务数据的情况下，做一个相对定量，相对符合逻辑的判断。

## 训练任务

通过对单元模型的理解，找到自己的关键模型，建立完整模型。利用benchmark校准模型，提升准确度。理解规模变化，能动态预测和调整单元模型。

## 训练工具

| 工具 | 类型 | 训练目的 |
|------|------|----------|
| 创始人算账段位图 | 方法模型 (Framework) | 明确算账段位，找到进步方向 |
| 单元模型和ROI | 方法模型 (Framework) | 明确算账段位与ROI的关系 |
| 十大典型单元模型 | 作弊小抄 (Cheatsheet) | 了解典型单元模型，破除盲区 |
| 选模型的三个原则 | 方法模型 (Framework) | 找到自己业务的核心单元模型 |
| 建单元模型三步法 | 方法模型 (Framework) | 提升构建完整单元模型的能力 |
| 找基准值的三个错误 | 方法模型 (Framework) | 避免错误，找准基准值 |
| 规模变化影响因素清单 | 自查清单 (Checklist) | 找到所有变化，动态预测模型 |
| 算账案例集 | 严选推荐 (Rankinglist) | 拓展算账信息面 |

## 模型：创始人算账段位图

![](images/e710413a2314763ffcffac2b36f535944ee5dff70cd4cef7013733837d0e7cef.jpg)

我们将创始人的算账能力分成6个段位，并且给出了相应的解法，希望能帮你避开典型的错误，大幅提升算账能力。

### L1: 不算账

这个段位是最差的。出来创业，都是一堆资源，一堆理想，一堆观点，一堆初心，但是全程没有数据和分析。

具体有三种典型错误：只谈初心，不谈变现，不谈数字。

如果你想从这个段位升级到L2，可以学习我们的单元模型系列课的第1节课，了解十几种常见错误，建立基本认知，快速地脱离这个阶段。

### L2：算错账

第一个段位，只要有了算账的意识，很快就能跨过去。而在这个段位，创业者开始算账了，但是不知道单元模型是什么，也不知道怎么选单元模型。

具体有三种典型错误：只算大账，存在盲区，选错模型

如果你想从这个段位升级到L3，可以学习系列课的第2节《选择核心模型》。课程里我们提供了十大单元模型供你选择，还会告诉你选完之后怎么做减法，找到你的核心单元模型。

### L3：有粗糙账

这个段位的创业者，已经有单元模型意识了，但是容易犯的错误是缺失各类关键项。这里我们只列出三个最容易被忽略的类型：

具体有三种典型错误：忽略经营成本，忽略组织成本，忽略财务成本

创业者想从这个段位升级到L4，可以学习系列课的第3节《构建完整模型》。

### L4：有完整账

算账做到这个段位的创业者已经及格了，能不重不漏地把单元模型的账算出来。但是，依然还不够，依然会犯错误。

具体有三种典型错误：缺少统计意义，错误类比，乐观预测

如果你想从这个段位升级到L5，可以学习系列课的第4节《找准基准值》。我们会告诉你如何找准基准值，如何优化你的单元模型。

### L5：有完整账+基准值

能做到这个段位的创业者，已经超过了85%的创业者，对行业的认知是比较深刻的。但仍然不是最好的，还会再犯一些错误。

具体有三种典型错误：忽略规模变化，忽略外部变化，忽略业务变化

如果你想从这个段位升级到L6，可以学习系列课的第5节《动态预测业务》。我们会带你梳理清楚未来的变化有哪些，以及如何对抗规模不经济。

### L6：有完整账+基准值+动态预测

到这个段位的创业者，我们已经能称之为顶级认知了。这个段位的创业者不仅知道业务的关键指标、基准值，还能基于规模、外部、业务等变化，来判断关键指标如何变化。

需要说明的是，这个段位很难达到。因为大部分创业者通常只是在个别一两个行业具体做过，而且还需要在这个行业深入研究后才能达到，所以，对自己不熟悉的行业很难做到L6。

## 模型：单元模型和ROI

![](images/9b4fc4b69eac45a9b62cab82db4fb5710657916dd1c2d6d435b76532776ab433.jpg)

单元模型跟ROI的关系是什么？

ROI是你在做决策时，一个反复说服自己形成笃定感的工具，是一个团队协作的工具，是一个你们讲道理的领导力工具。

单元模型，是你公司层面，最重要的ROI模型。

单元模型作为ROI，特殊在哪里？多数决策是一次性的，但是单元模型是你公司那个反复在循环的ROI，是你整个公司的立命之本，是你公司的商业模式。

## 小抄：十大典型单元模型

算账模型从L2到L3段位核心要解决的就是选模型的问题。那么如何选模型呢？首先，你要知道到底有哪些单元模型。

我们发现大家的创业项目各不相同，对于不同的业务，单元模型的差别非常大。能不能总结几个典型的单元模型，帮助大家扫清单元模型的认知盲区呢？

于是，我们分析了我们投资过的项目和所有的学员案例，又经过了几轮的内测，总结出了十大典型的单元模型。这些模型不敢说是覆盖100%的项目，但是应该能覆盖99%的创业项目了。

为了方便理解，我们把它们分成了3类，分别是按销售、按人头、按空间来建立单元模型。

| 分类 | 模型 |
|------|------|
| 按销售 | 单订单模型<br>单SKU模型<br>单用户模型 |
| 按人头 | 单客户模型<br>单销售模型<br>单履约模型 |
| 按空间 | 单柜子模型<br>单门店模型<br>单商圈模型<br>单城市模型 |

你可以根据自己对业务的理解，从10个模型里筛选适合的模型。

## 模型：选模型的三个原则

当我们根据十大典型模型找到自己业务适合的模型之后，面对这些模型怎样通过一些判断逻辑，排个优先级，抓到里面最核心的那笔账呢？我们总结了三条筛选原则。

### 原则1：可以完整定量

单元模型是用来算账的，所以它至少是一个基于数据的定量的模型。这是选模型的第一条原则，也是最基本的原则。

换句话说，如果这个模型中的一些关键的收入成本项，你没法拿到一个相对准确的值，没法定量，那这个模型优先级就极低，甚至不需要算。

### 原则2：考虑依赖关系

单元模型在逻辑上是存在层级的，存在先后顺序的。你为了算核心单元模型，很可能需要先算某个单元，才能去算这个单元。

比如，做线下业务，算商圈或城市模型时，一般也需要先算清单店模型，才能更好地计算更大的一个商圈、一个城市的收入成本结构。

再比如，在线教育通过投放获客，主要看单用户模型，在计算LTV的时候，需要先算单订单模型，一个订单能赚多少钱。

### 原则3：驱动增长优先

这是最重要的一个原则。

你的业务驱动力可能有两三个，但是一般核心驱动力在特定时间就只有一个。越是你的业务核心驱动力，优先级越高，越要先算清楚。

怎么判断哪个模型是你的核心驱动力呢？

如果业务想持续增长，只要这个模型持续复制。你的整体收入或规模，就能持续增长，甚至呈现线性规律。那么，这个模型就是你的核心驱动力。简单说就是，这个核心的单元模型复制10倍，你的整体收入也可以增长接近10倍。

## 模型：建单元模型三步法

单元模型升级到L4的关键在于构建一个完整的单元模型。关于如何构建一个完整的单元模型，我们总结了三个步骤：建单元模型三步法。

### 第一步：跑业务闭环

什么是业务闭环呢？简单来说，就是跟踪一个单元，推演它在各个业务环节里发生的故事，找到这个过程中产生的所有成本和收入。

这个单元可能是一个订单、一个用户或者一个柜子，你需要把它代入到真实的业务里，认真地梳理每个环节产生了哪些收入和成本。

在这个过程，如果你梳理得足够细的话，你会发现很多之前没有注意到的事情。

### 第二步：按清单检查

我们收集了市场上各个单元模型容易犯的错误，整理成了一个非常长的自查清单，包括经营、组织、财务这3个部分最容易遗漏的关键成本项。

# 模型：找基准值的三个错误

从L4提升到L5段位的难点在于找准基准值。行业基准值（Benchmark）是创投圈非常高频的一个词，在大家分析业务的时候，经常会利用Benchmark来做各种评估和判断。

那么，什么是行业基准值呢？你可以把它理解为可以被定量的行业常识。这个值一般是通过专家经验，或者通过多个数据值统计得出的。

当你评估单元模型是否可行时，很重要的一种判断方法就是基准值。那么，这时候基准值的准确性对你的判断影响会非常大，所以如何找准基准值是判断的关键。

我们总结了大家最常见的3种错误，如果你避免了这些错误，就能找到柜对准确的基准值。

错误1：缺少统计意义。在找基准值的时候，无论是在时间还是空间上，你的数据都要有统计意义。举两个很典型的例子：

比如：一些业务，因季节和月份不同而收益不同。如果你用旺季的数据来评估你的单元模型，那肯定是不准确的。

再比如：一些业务，不同城市的收益不一样，如果你只用一线城市的数据来评估，

么在复制到二三线城市的时候，一定是有偏差的

错误2：错误类比。没有考虑前提条件，错误地去类比相似模式的数据

比如，某个创始人在做旅游社区020时，参考趣头条的留存率 $50 \%$ ，认为留存率可以做到 $30 \%$ 。但实际上，这两类业务的需求频次差距很大，这样类比是有很大偏差的

错误3：乐观预期。基准值通常是一个范围值，很多人总喜欢用最好的值来评估单元模型。

但到了实际运营的时候，你会发现几乎不可能达到预期，以至于最后单元模型根本跑不通。

# 50|以太一堂·内部训练手册

# 清单：规模变化影响因素清单

如果你已经处在L5段位，距离L6顶级认知只有一步之遥了。如何跨过这一步呢，关键点就是动态预测。

我们要时刻记住，单元模型的核心是为了复制，所以随着单元模型的复制，规模的变化一定会对我们的单元模型造成影响。

具体会有哪些影响，我们总结了一个清单，你可以对照这个清单来动态预测你的单元模型。

<html><body><table><tr><td colspan="3">规模变化影响因素清单（checklist）</td></tr><tr><td colspan="2">规模经济</td><td>影响因素</td></tr><tr><td rowspan="7">变优 规模不经济</td><td rowspan="7">良好</td><td>1.网络效应（用户越多，价值越大）</td></tr><tr><td>2.双边市场（平台模式） 优秀3.无形资产（独家授权/牌照/专利/KnowHow）</td></tr><tr><td>4.用户迁移成本（2B/数据类/习惯培养类典型） 5.品牌的规模效应（ToC/TOB/ToG）</td></tr><tr><td>6.营销的规模效应（做投放/做广告） 7.融资的规模效应（融资/资金获取成本）</td></tr><tr><td>8.业务节点规模效应（集中获客/销售BD/技术研发） 9.集采优势（三环节：原材料加工/履约仓配）</td></tr><tr><td>10.创新容错更高（现金流，持续试错而不死）</td></tr><tr><td>一般11.数据和案例优势（典型如SAAS，案例越多价值越高） 12.大公司优势（合伙人变NB，招人容易）</td></tr><tr><td></td><td></td></tr><tr><td rowspan="8">变劣</td><td rowspan="8">4.议价沟通（涉及到非标准化的议价，就会不经济）</td><td>影响因素</td></tr><tr><td>1.人的招聘培训（往往难点：市场供给少，上岗时间长） 2.人的日常管理（专家<白领<流水线工人<机器）</td></tr><tr><td>很难3.特殊岗位管理（最典型就是腐败问题）</td></tr><tr><td>5.对外沟通（涉及到非标准化的沟通，难度更大）</td></tr><tr><td>6.跨城市扩张（典型如养老院，政府关系搞不定）</td></tr><tr><td>较难7.产品质量下降（供给有限，规模上来平均质量下滑，影响收入or口碑）</td></tr><tr><td>8.合规成本（小公司随便搞，大公司受监管，要合规）</td></tr><tr><td>9.品控变难（规模化导致品控变难，品控成本变高） 10.客诉变难（客诉处理和品牌影响，变难）</td></tr><tr><td rowspan="2">尚可</td><td>11.加盟商管控成本变难（典型问题是加盟商造反）</td></tr><tr><td>12创新路径依赖（组织越成熟，对新业务越不利）</td></tr></table></body></html>

# 严选：算账案例集

很多同学在学习算账课星时，会来和我们感慨算账能力还需要提升，想多获取一些信息来学习，但网上的资料太少了，希望我们提供更多的信息。

市面上关于单元模型的资料确实很少，很多内容看了也应用不上，这里重点推荐5篇，大家可以扫描下面的二维码，认真阅读一下。

1.《一篇文章教你用数学公式看透商业模式》Unit Economics一般是一页表格的数据，创始人利用它可以理清该商业模式下某个最小运作单元的运作方式，是用一种三维的方式体现整个商业的逻辑。

2.《创业者视角的项目评估模型》长久以来，不断有创业者问您看我这个项目到底行不行？"几年前的本文作者是无法回答的，因为他也不知道怎么判断。后来他的回答一般是，这个之前有人做过，好像没做出什么花儿来，但其实他也无法深入项目本身去拆解这个问题。直到今天，感觉能总结出来一个模型，帮助他回答这类问题。

3.《实体零售的单店模型和连锁模型》单店模型建立之后，就可以开第一家店试运营，并根据实际数据持续调整各项因子和参数，将坪效和毛利率提升到最佳值之后，开始设计连锁模型"。

4.《业务敏感性"之如何计算单位经济模型"》金沙江创投合伙人朱啸虎在2019年底提到讲故事烧钱创业的时代远去，更看重项目的单位经济模型"。从融资方获得的众多资料中，哪些核心指标能够帮助建模呢？本文以在线教育平台H为例，模拟建立单订单经济模型"。

5.《SaaS指标百科全书（上中下）》关于SaaS指标的重要性，我们已经了解得足够多。这一次，让我们直接一点，进入指标的海洋一在SaaS的世界里，P&L不是宝洁公司的山寨品牌，LTV跟电视台没关系，CAC更不是什么乙二醇乙醚醋酸酯；了解名目繁多的指标不仅能让你在谈吐间展示专业的自我，更能帮助你深刻了解自己的业务。

# 52|以太一堂·内部训练手册