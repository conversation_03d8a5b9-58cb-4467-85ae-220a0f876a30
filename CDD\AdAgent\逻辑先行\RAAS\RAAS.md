# 音频转录内容

所以这就第一次出现了，其实AI它并不是要给你一个工具，它已经不是软件进化了，而是AI即结果。我觉得整个庞大的软件生态都可能会被重构。

当你提出一个查询的时候,在PC时代、在软件时代，你应该怎么得到你的输出呢？你肯定进入到了一个流程里，花了很长时间去搭建结构，不论需要用什么软件、用什么数据，你构建的结构化信息  然后得到一个输出。当在这个过程你叫它软件也好，那进化成SaaS也好，其实就是这样。

当然SaaS里面还有一个天然的特点，就是SaaS里面它会有一种自我细分和自我内卷的倾向，因为它是线性的。所有线性的问题你都易于分割，比如说我做了一个ERP系统，有另外一种创业就是我只做ERP系统里面的供应链，然后再过两年说我可以做ERP系统里面预算，我可以做商业智能，它就不停地从这里面往细分切，它会越切越细，越创业越细，这个市场变得越分散。

但是到了人工智能时代，我觉得发生了一个很有意思的变化。就是说整个人工智能它跳过了你要做软件和做结构化数据的这个过程，它直接走向了原始数据。你有出入户单，你有这个底层的发票，你有这些合同，实际上它可以在原始数据里面按照你的需求去加工。
![1748738415531](image/RAAS/1748738415531.png)


所以这就第一次出现了，其实AI它并不是给你一个工具，AI是成为你的同事，它理解你的想法，它直接帮你完成工作。它已经不是软件服务了，可能是AI即结果。

就从AI直接到结果出现。在这里面我觉得整个庞大的软件生态都可能会被重构，因为不再需要通过IT把流程转化为软件的过程，这个中间环节不存在了。所以在这个变革里面我觉得会有大量的崭新的机会，可以把现在所有的企业流程类软件，包括ERP在内，包括财务系统，包括内部的人事系统，它全部都可以重构。当然也可能是一个模型就把这些都做了。