
1. 当Coding的成本降低,




1. 大量的问题是脚本解决的,而不是产品化

  我从2011年开始就开始写代码来解决业务的问题，主要在流量和运营侧。 
  这些代码中只有20%的是产品化。
  80%是散落在各处的脚本。
  
  我现在在做的出海业务,手机,卡,IP。只是做业务角度来看，能写代码,很多时候也许并不需要一个界面？
  为什么不做产品化？
  1.是本来就是流量,运营

2，什么场景下，产品化能带来更大价值




 2. 产品化算账问题？产品化的成本
     传统产品化 我需要找1个产品  1个后端 1个前端  1个安卓端  2-3个月 。成本预估
     一个产品要

    在中国做SAAS 是一个挺烂的生意模式



3.  AI来做  每个环节都是对应的一套提示词。






4. 每个环节具体是怎么做的
  后端 前端 安卓端
  产品原型  
  内容文章  官网 PDF

5. 写10万行代码 和写 10万行提示词的区别
   我Vibe Coding 了一整年。
   写10万行提示词 是新一代牛马的饭碗