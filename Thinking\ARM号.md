
# ARM号的能力
**概述**: 批量运行、注册、养号海外APP账号
基于魔云腾 ARM云手机方案
已有成熟产品参考：
- **TK云大师**: https://www.tkyds.com
- **WhatsCRM**: https://www.xrobot.tech



# 方向性关键决策
决策1: 魔云腾方案是否产品化？  
       不产品化
决策依据:
批量搞号的能力适合做大客户合作,不适合产品化。

决策2: 魔云腾方案不产品化CDD的内容要写吗?
要写，但要讲究策略和艺术。
决策依据:
 通过展示技术深度和解决问题的能力，建立技术权威和品牌信任。


决策3  魔云腾方案的 MVP具体标准

技术测
1. 魔云腾SuperSDK 跑通
   SuperSDK-解决多台云手机的创建和管理。
2. 魔云腾 RPASDK 跑通
   RPASDK - 解决单台云手机内APP具体的操作
3. Clash 二开 跑通
  可以远程设置链式代理,从而及时更新设备IP

业务侧
1. 中转网络和落地网络IP资源
2. 邮箱,esim号的资源

管理测
1. 后台管理系统
  管理账号,设备,IP的系统


# 邮箱和esim号的产品化
邮箱产品化：
https://www.cuiqiu.com
多域名邮箱 来赞宝的运营在使用
核心场景 大量管理shopee lazada等店铺

大量临时邮箱API  
如 https://temp-mail.io/zh  30刀1万个收件
https://mail.td/zh  


esim产品化
方向1 --9esim APK 二次开发,提供切换手机号功能。
方向2 --直接接码  蜂鸟云号

