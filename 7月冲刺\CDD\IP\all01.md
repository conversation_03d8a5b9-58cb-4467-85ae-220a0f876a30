# 出海三件套最后一环：为什么IP这么复杂？

出海三件套：手机、卡、IP。

水最深的就是IP了吧。

0播放了，掉橱窗了，什么问题都可以让IP背下锅。同时市面上又有各种各样的解决方案：小火箭、软路由、SDWAN、专线、机房IP、住宅IP...

价格也是个谜，从一个月10几块到一个月1000-2000。价格跨度巨大，对于小白来说，肉眼看起来好像又没有什么区别。

今天我就从10年前网游加速器的经历说起，彻底讲清楚IP这件事的本质。

## 网游加速器的那些年

2014-2015年，我加入一家网游加速器公司，当时是TOP3的加速器。负责搞流量。

网游加速器这个产品是怎么来的？

就不得不提英雄联盟和暗黑破坏神3这两个标志性的游戏了。我隐约还记得，这两款游戏的营收占了整个公司大盘的80%以上。

英雄联盟 2014-2015年，韩服（KR）是公认的LOL"圣地"，职业选手和顶尖路人云集。去韩服打Rank是证明自己、提升技术的最佳途径。风气更好,普遍认为外服（尤其是韩服）的游戏环境更纯粹，玩家求胜欲强，很少有国服当时常见的"演员"和"20投"现象。当时游戏直播刚刚兴起，大主播们"征战韩服"是重要的直播内容，吸引了大量粉丝模仿，也想去体验一下。

暗黑破坏神3,全球发售是2012年，而其资料片《夺魂之镰》是2014年3月上线的。但国服直到2015年4月才正式公测。在这之前，中国玩家想玩这款大作，唯一途径就是去玩亚服、美服或欧服。 暗黑3的赛季模式是核心玩法，全球同步开启。想和全球玩家一起"开荒"，就必须去外服。

## 网游加速器核心解决3个问题

```
问题1: IP地理限制    问题2: 网络速度      问题3: IP质量
     ↓                  ↓                ↓
   能登录            能流畅玩           不被封号
     ↓                  ↓                ↓
  美国IP地址        低延迟+不丢包      干净独立IP
```

1. IP地理限制,不能登录美服、韩服游戏
2. 网络速度慢,延迟高经常丢包
3. IP质量,会被游戏服务器封号

## 10年后的重新思考

10年前我主要搞流量。后来做了运营，干了电商，成了一个全栈开发。

最近和当年的技术团队再沟通，聊了聊网游加速器的技术实现，有了技术背景，沟通起来就顺畅得多。猛然想起当年开会讨论的技术问题，现在竟然又能理解了。

聊完之后也交叉验证了：网游加速器解决的问题，和现在出海矩阵或者出海私域遇到的问题，本质上是一样的，但侧重点又不太一样。

## 拆解3个核心问题
## 问题1：IP地理限制
平台检测IP地理位置，限制中国用户
 一个美国VPS就够了

```
┌─────────┐      ┌─────────┐      ┌─────────┐
│  你的   │      │  美国   │      │ TikTok  │
│  设备   │─────>│  VPS    │─────>│ 服务器  │
└─────────┘      └─────────┘      └─────────┘
    🇨🇳              🇺🇸              🇺🇸
```

工作原理
- TikTok看到的是美国IP，不是中国IP
- 绕过了地理限制，可以正常访问
- 但这个只是能登录了，体验嘛...就一般般


## 问题2：网络速度

**本质：** 直连延迟高、不稳定

现实的物理和网络障碍是巨大的。不使用加速器，直连外服会遇到以下致命问题：

**物理距离导致的硬伤：高延迟**
数据传输速度受光速限制。从中国到美国/欧洲的游戏服务器，数据一来一回，天然就会产生150ms-300ms的延迟。

这是什么概念？
- LOL里，你的技能会慢0.2秒放出，你看到的敌人位置其实是0.2秒前的，你永远躲不掉关键技能，体验极差。
- 暗黑3里，尤其是在专家模式（Hardcore），一个延迟就可能让你反应不及，导致角色永久死亡，所有心血付诸东流。

**跨国公网的顽疾：严重丢包**
你的游戏数据在去往国外服务器的路上，要经过无数个公共网络节点，尤其要挤过那个拥堵不堪的"国际出口网关"。

这就好比高峰期开车，不仅开得慢（高延迟），还随时可能因为堵死而"丢车"（丢包）。

游戏里丢包的表现就是：人物瞬移、卡在原地不动、技能按了没反应、突然掉线。这比稳定的高延迟更让人抓狂。

解决方案：使用专线

```
【直连方案】延迟: 300ms+，丢包率: 高
┌─────────┐                              ┌─────────┐
│  你的   │                              │ 美国    │
│  设备   │─────────────────────────────>│ 服务器  │
└─────────┘                              └─────────┘
    🇨🇳                                      🇺🇸

【专线中转】延迟: 170ms，丢包率: 低
┌─────────┐      ┌─────────┐      ┌─────────┐
│  你的   │      │  香港   │      │  美国   │
│  设备   │─────>│  中转   │─────>│  落地   │
└─────────┘      └─────────┘      └─────────┘
    🇨🇳              🇭🇰              🇺🇸
    50ms            100ms            20ms
```

**优化原理：**
- 中国→香港距离近，延迟低
- 香港→美国线路质量好，丢包少
- 总延迟降低，稳定性提高



### 问题3：IP质量问题

大部分网游加速器公司都是买几十个IP，所有玩这个游戏的玩家共用这几十个IP。很长一段时间都没什么问题。

我隐约还记得，有一段时间,某几个游戏对IP的要求特别高，导致用这个IP池的账号开始封号，也不是全封，有一定比例的账号会被封。大概率是某个玩家用了外挂，导致这个IP下的所有游戏账号被连坐。

后来的解决方案是提供独立IP。独立IP就是只给一个玩家用,
就不会出现封号现象了，当然了要加钱。

## 游戏场景 vs 营销场景：需求大不同

了解了这3个问题，我们再来看不同场景的需求差异：

```
┌─────────────┬─────────────┬─────────────┐
│    维度     │  游戏场景   │  营销场景   │
├─────────────┼─────────────┼─────────────┤
│ 延迟要求    │ 极其苛刻    │ 相对宽松    │
│             │ (<50ms)     │ (<200ms)    │
├─────────────┼─────────────┼─────────────┤
│ IP要求      │ 相对宽松    │ 极其苛刻    │
│             │ (共享IP池)  │ (独立IP)    │
├─────────────┼─────────────┼─────────────┤
│ 主要成本    │ 专线费用    │ IP费用      │
│             │ (几十万/月) │ (几千/月)   │
├─────────────┼─────────────┼─────────────┤
│ 技术方案    │ 专线+机房IP │ 公网+住宅IP │
└─────────────┴─────────────┴─────────────┘
```

### 游戏场景特点
- **延迟要求：极其苛刻**（玩家容忍度接近零）
- **IP要求：相对宽松**（30-50个IP池轮流使用）
- **成本：极高**（10年前一条专线几十万/月）主要花在了线路上

### 营销场景特点
- **延迟要求：相对宽松**（能流畅刷TK视频即可，直播除外）
- **IP要求：极其苛刻**（轻则0播放，重则封号）
- **成本：主要花在了独立IP上**

理解这两件事的本质差异，再看开头提到的"水最深的就是IP"。


还记得上面我们分析完的网游加速器要解决的3个核心问题吗？
1. IP地理限制,不能登录美服、韩服游戏
2. 网络速度慢,延迟高经常丢包
3. IP质量,会被游戏服务器封号

在营销场景在其实简化成了2个核心问题
1. IP地理限制,不能登录TikTok,FB等
2. IP质量,会被TK0播,封号

## IP地理限制
【直连方案】延迟: 300ms+，丢包率: 高
┌─────────┐                              ┌─────────┐
│  你的   │                              │ 美国    │
│  设备   │─────────────────────────────>│ 服务器  │
└─────────┘                              └─────────┘
    🇨🇳                                      🇺🇸

在营销场景下可以采用直连方案,直接买对应国家的服务器,进行搭建。比如你做TK美区，直接买洛杉矶机房的服务器。做欧洲市场,买英国的服务器，做东南亚，直接买新加坡的服务器。
一般买一些知名的服务器服务商，比如某瓦工，每个服务器会有一个独立的IP,这个IP会不会跟别人一起共享使用。

## 1000个TK账号,需要买1000台服务器吗？
![alt text](局部截取_20250715_163758-1.png)

一个服务器是把CPU,内存,硬盘,流量打包在一起出售的。你想要多个IP就需要买多个服务器。
 聪明的你是不是在想，买1000台服务器实在是太贵了。你只想要1000个IP,其他的CPU,内存,硬盘买一台服务器就行了。
 所以能不能1台服务器+1000个IP呢？
 答案是可以。

## 代理IP服务商
![alt text](局部截取_20250715_164849.png)
922S5proxy,ipipgo,cliproxy等等这些都是代理网络服务商。
![alt text](局部截取_20250715_165138.png)
比如美国的一个家庭住宅独立IP，一个月大概在30元。

既然有这样的的代理服务商,提供了美国的住宅IP，不能直接用这个IP吗？
不能，仔细看,这些代理IP服务商都会提示说 不接受中国IP直连。至于为什么,你猜。

还记得上面说的网游加速器的第二个问题 网络速度，
通过网站中转来解决了速度问题。

这里同样可以用网络中转来解决这些代理IP服务商不能直接使用的问题。通过中转IP先到某个国家,比如漂亮国，然后就可以用这些代理IP。


所以只需要 
1. 买1个目标地区的服务器
2. 在代理IP平台买目标地球独立IP
通过链式代理配置就能实现 一台手机一个独立IP。

和管理大量手机卡一样，管理大量手机的网络也是个巨头疼的事情

我做了一个工具来解决。
1展示中转网络界面截图
 已有截图,待添加
2.展示落地网络界面截图
  已有截图,待添加

