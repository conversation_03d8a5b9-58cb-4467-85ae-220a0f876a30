
1、需求的诞生
从日常动作延伸出为什么有这么一个需求


## 二、商业价值的发现

"现在太卷了，做什么都亏钱..."
这句话，你我都听过太多次。

有意思的是，总有那么一小撮人，在默默捡钱：
- 18年的时候，在朋友圈投放APP，一个注册成本10元。同样在朋友圈投放关注公众号，多少钱呢？2毛。获客成本差50倍，转化率也能差50倍吗？
- 24年私域流量已经卷到回本需要6-8个月的时候，在小红书投女鞋直接购买链路，当天ROI竟然可以做到1:5。

"别人投放的每一个广告，都是一次市场验证。"
"验证是要花成本的，多看看竞品的广告，能省几百万的试错成本。"
这些信息，比任何商业咨询都值钱。
因为这是别人真金白银砸出来的结果。

## 三、痛点的确认

现有的广告产品没办法满足我的需求：我需要的，是我在日常刷到的那些广告，而不是所有行业的所有广告。

为什么这么说？

1. 我能刷到的广告，往往最有价值：
   - 平台会根据我的兴趣推荐相关广告
   - 大多是我工作过的行业或者做过的项目
   - 这些广告背后，藏着我最容易复制的机会

2. 自己收集：一地鸡毛
   当突然想起来某个广告的时候，打开相册发现：
   - 乱七八糟，无从下手：
     各种APP，各种样式的广告，交杂在一起
     想搜索 发现搜索不了,只能一个个打开看
   - 重要信息，总是不全：
     - 只截了头层广告,没有截落地页
     - 只存了主图，评论区的金句没记录

我需要一个产品：
- 能自动在手机刷新各种APP,看到的广告后代替我手动截图
- 能有一个后台展示我刷到过的各种广告

## 四、从想法到行动的挑战

在打工的时候，一切看起来都很简单。老板充满激情地确定方向，产品经理拿着详细的PRD，设计、后端、测试都配齐了，拉个评审会就能开干。

但当你作为独立开发者，一切都不一样了：
- 我遇到的问题，别人是否也有同样的痛点？
- 这个需求是刚需还是可有可无？
- 这确实是一个需求，也有痛点，用户愿意为这个痛点买单吗？

## 五、独立开发的挑战

作为独立开发者，面临的挑战是全方位的：
- 自己发现需求
- 自己评估可行性
- 自己设计原型
- 自己开发
- 自己测试
- 自己部署
- 自己推广
- 自己维护

这意味着需要在有限的资源下，承担更大的风险和更多的责任。但好在现在大模型技术日新月异，为独立开发者提供了新的可能性。

我觉得还是要试一试。
