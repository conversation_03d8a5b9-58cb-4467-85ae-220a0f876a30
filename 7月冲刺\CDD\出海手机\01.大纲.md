
核心主题： 
现在做TK的短视频带货项目。
我在国内已经有100台手机在跑矩阵和私域项目.
如何无痛的把国内的手机基建迁移到海外项目

观点1：为什么要基于现有基建迁移（而不是重新搭建）
      1.各种信息高速我的都是用iphone8
      2.我已经有红木note9.note11正在跑的100多台机器,以及基于这些手机做的各种自动化基建（多年投入搭建）
      3.3. 用iPhone8搭一套新基建的成本过高：
   - 核心问题：基于iPhone的自动化群控技术成本更高
   - 现有的安卓自动化脚本全部用不上
   - 需要重新开发整套自动化系统

      最优的解决方案还是基于这些已有的100多台手机的基建

观点2：现有国内设备迁移海外的技术障碍
- 核心问题：缺少GMS服务
- 对现有业务流程的具体影响
    1. 无法下载和使用海外APP-GooglePlay
    2. 谷歌账号的生态,大部分海外APP都是绑定了谷歌账号登录。


观点3 解决GMS的方案
    1. 把国内版刷到国际版系统有
    2. 买到国际版的手机
      小米手机是有国内版和国际版。
      国内版因为没有GMS
    3.展示GMS手机安装和更新APK
    4.对比国内和国外版红米note9
    外观 配置  价格
    买入钩子从华强北渠道购买来的

观点4 国际版手机的额外好处
    1.插入国内卡,手机和流量正常使用
    2.用国际版手机装国内APP,之前的基建正常使用。
    3.一套设备同时支持国内外业务
    4. 还有一个更不一样的收获
     国内的APP对海外环境的用户,风控不太一样，目测是宽松的多。

最后的的结论是采购国际版手机,价格一样。可以做国外,也能做国内。
       