# 基本功3：挖需求/需求分析

大家出来创业，最最最悲剧的结果是什么？是方向没选对？团队人心不齐，队伍解散了？还是把项目做成了一个生意？

这些都不是最悲剧的。最悲剧的结果是，你非常非常努力，但是毫无意义。

所以，管理一家创业公司，商业模式不是我们的第一工作，需求分析才是。

需求为什么重要？因为这是所有创业项目的「逻辑原点」：找到一个真正值得被解决的问题，这是所有公司创业的原点。

你的这个原点分析得越清楚，你后面的决策质量越高。

训练任务：理解需求分析的六个段位，学会使用需求分析的四字口诀，以及学会用需求三角来分析自己业务的用户需求。

训练工具：

1. 需求探索修炼地图：方法模型（Framework），明确需求分析段位，找到进步方向
2. 需求分析四字口诀：方法模型（Framework），学会使用拆推评算，深入分析需求
3. 需求评估三角形：方法模型（Framework），学会从三个维度，来评价一个需求

# 模型：需求探索修炼地图

需求分析，是有段位差距的，同一个业务，不同操盘手来分析差异巨大。

一堂需求探索修炼地图

![](images/923d9a1165369a9c2d67de776594088ff60eb3ef61d5d70f57bd1a2b6e08c06c.jpg)

# 模型：需求分析四字口诀

无数的公司，日夜弹精竭力，耗尽人力物力，每天起早贪黑，干到最后发现：原来解决了一个不够重要的问题，其至说，做了一个没人要的产品。

所以，我们说：需求分析是创业的原点！你的业务到底解决了用户什么问题，你对这个问题理解得足够透彻，后面创业的每一步才能有意义。

那么，一堂五步法的第一步"需求"，究竟该怎么分析呢？为了方便记忆，我们总结了一个四字口诀：拆、推、评、算。

![](images/e8317b5f82b7a6bdab7e30442b8ae18bff300deee00d9238e5448be1ee797560.jpg)

第一步，拆，拆解：认真地拆解用户最真实的需求。这是一个必会的基本功，这一步的核心是要培养好的用户视角，明确你的细分用户。

第二步，推，推演：推演用户使用场景，理解问题。具体怎么做呢？其实就是站在用户视角，通过真实的场景推演，回答一个问题：什么样的细分用户，在什么场景下，遇到了什么真实问题。

第三步，评，评估：定性评估需求可行性，判断价值。这一步，可以利用"需求三角"这个工具进行定性分析。关于需求三角的具体内容可以参考下一页。

第四步，算，计算：定量估算市场大小，理性思考。关于业务天花板，有两个不同的视角。一个是业务视角，用来指导决策，要尽量理性清晰，建议适当保守。另一个是融资视角，要符合专业投资人的审美和话语体系，要适当包装。

# 模型：需求评估三角形

定性评估需求可行性，我们最常用的分析工具就是"需求三角"。

任何一个需求，都有三个维度：普遍性、频次和刚性。通过这三个维度对一个需求进行打分，可以快速完成定性，或者一定程度的区间定量，进而分析需求的可行性。

![](images/9da42642a6676b8b4573f85dc88e0562f98f8e5a52188157795e5a9036fafd28.jpg)

1. 普遍性：目标用户里，有多少的细分用户真的有这个需求？
2. 频次：发生问题/使用产品的频次是多少？一生/年/月/周/日时
3. 刚性：可用可不用？免费我才用？哪怕付费/加钱我也非用不可？

那么具体如何打分呢？因为大家的项目不太一样，核心要抓住一个要点：和同行比较。

任何一个项目，没法孤立看好坏，更多是在同类模式相互比较着看。

比如，在一系列的消费品、一系列的电商、一系列的企业服务中，可能一个品类频次低留存做不起来，但是另一个品类可能就行。或者说，这个需求很窄，获客成本会很高，而另一个普遍性高，获客成本会低很多。

所以，这个打分没有固定的、标准的数值用来判定多少分就是好的需求，核心是对比同行来进行评估。