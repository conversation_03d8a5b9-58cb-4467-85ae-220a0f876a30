# 基本功2：拆业务/关键假设

在创业过程中，我们经常会遇到两类创业者：

第一类是X型创业者，他们总是在想：我们要不要做这个事情？这个事情靠谱吗？这个事情能不能成？

第二类是Y型创业者，他们总是在想：我们要怎么做这个事情？这个事情怎么做能成？

其实，这两类创业者都有一个共同的问题：他们都在思考关键假设。

关键假设，就是你认为这个事情能成的关键点。它是你对这个事情的判断，是你对这个事情的预期，是你对这个事情的信心来源。

## 两类创业者的对比

![两类创业者对比](images/6ae25fc59156e0c8f5bddbd75acd898256b09d4f640550c2b274ce9066a4fd14.jpg)

1. X型创业者
   - 认为创业 = 做一个产品
   - 习惯做一个大而全的完整产品
   - 推进宏大的创业方案
   - 需要大量时间和资金投入
   - 效率低下

2. Y型创业者
   - 用假设驱动业务
   - 关注业务的关键假设
   - 通过各种手段快速验证假设
   - 创业推进快准狠
   - 效率很高

## 关键假设的重要性

![抓不抓关键假设的区别](images/0b476c05053b7fca2dee4b117d096b4fd15c5e13fc22f03d6e61e64aeab82bfb.jpg)

![只抓最重要的关键假设](images/1a4e1dd8d1686d9fe00fd1c0f1ed8c77cba0b3611d21a1f4cb95045feafa5392.jpg)

## 训练任务

用259模型拆解自己的业务，清晰理解项目背后的逻辑，找到并高效验证关键假设。

## 训练工具

| 工具 | 类型 | 训练目的 |
|------|------|----------|
| 关键假设三板斧 | 方法模型（Framework) | 用Y模型来指导自己科学做事 |
| 堂259假设 | 方法模型（Framework） | 找到项目需要验证的关键点 |
| 堂创业五步法 | 方法模型（Framework） | 拆解创业的5个必经里程碑 |
| 堂商业模式画布 | 思考画布（Canvas） | 按9个部分完成业务拆解 |
| 验证关键假设的顺序 | 方法模型（Framework） | 理解假设验证的先后顺序 |
| 验证关键假设的方法 | 方法模型（Framework) | 掌握常见验证假设的方法 |
| 关键假设ABCD模型 | 方法模型（Framework） | 清晰理解关键假设的应用场景 |
| 提升团队优质想法的三套工具箱 | 自查清单（Checklist） | 打造团队关键假设的思考习惯 |

## 模型：关键假设三板斧

关键假设的讨论必须基于明确的目标。关于假设验证，可以分为三个环节：

1. 先加法：把业务拆成一组假设，找到影响结果的关键前提
2. 再减法：从中挑出重点，把注意力放在关键假设上
3. 快验证：用10%的投入完成100%的工作

## 模型：一堂259假设

拆解关键假设的三个工具：

1. 拆成2部分：价值假设和增长假设
   - 价值假设：产品对用户的价值，是否愿意买单
   - 增长假设：业务是否能快速复制、扩张、传播

2. 拆成5部分：一堂五步法

3. 拆成9部分：一堂商业画布

## 模型：一堂创业五步法

![一堂创业五步法](images/1e70373ae5a475b9ae6161ffed1d2510380d89c4b309555da7ff4646e83a5310.jpg)

创业的五个核心里程碑：
1. 需求：找到值得解决的问题
2. 解决方案：能否满足用户需求和痛点
3. 商业模式：收入是否大于成本，是否可持续
4. 增长：能否快速复制模式，占领市场
5. 壁垒：能否守住竞争优势，形成垄断

### 五步法爬山地图

![一堂五步法爬山地图](images/cb100d14bf42e685ba17e289ae6d2e7c4eeed2c5767a95a3cbe7fe7c99309f1a.jpg)

注意事项：
1. 只是商业分析水平，不是人的操盘段位
2. 要用成长的眼光看，段位会持续提升

## 画布：一堂商业模式画布

一页纸拆解商业模式的9个关键部分：
1. 细分用户
2. 需求
3. 核心卖点
4. 解决方案
5. 收入
6. 成本
7. 核心指标
8. 获客渠道
9. 壁垒

## 模型：验证关键假设的顺序

![验证假设顺序的3个原则](images/5cf53ae3151233bb8938f7342cf0c416a73c5a365c844d9103270fda2f63c560.jpg)

三个原则：
1. 前置假设优先：按照从需求到方案的顺序验证
2. 风险高的优先：先验证风险特别高的项目
3. 验证单一假设：每次尽量只验证一件事情

## 模型：验证关键假设的方法

回答关键假设的三种方法：

1. 靠常识，用行业经验回答
![依赖经验常识](images/756d76db6350c1528a17cd639f5797f813fe08151e5a295a40b7e2ed5155c294.jpg)

2. 靠调研，通过对手回答
![通过对手回答](images/7e0d23106e4c7cf1cee06063352675bfff02dd5bafdec1155058fb2a54526155.jpg)

3. 靠验证，设计实验测试
![设计实验验证](images/f244a5bcf0bb43531b2c4b15fd9137b4e1f7ed0c1fd7f2a6ce2e60389b06645d.jpg)

重要提示：如果能清晰理解业务背后的逻辑，大幅降低创业成本，你不需要融资和组建团队就可以用低成本验证你的关键假设。