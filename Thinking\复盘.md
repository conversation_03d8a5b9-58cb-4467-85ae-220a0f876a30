# 产品1 - 广有财DASS
时间:2025年2月 - 2025年6月
想做的产品:信息流广告情报DAAS

# 数据获取TPF  2025年2月-2025年4月 60天
决策是通过xposed获取广告数据
开发时间 (技术验证)  60天
结论 TPF开发完
获取数据是用的  xposed模块
自动化控制集成的 autojs
通信是用的  腾讯IM
## 决策改变
因为Xposed的合规问题,转向AutoJS方案

# 数据获取TPF-Autojs  2025年4月-5.15 45天
决策是通过autojs无障碍的方式来获取广告
开发时间 (技术验证)   60天
结论 TPF开发完
实现用Autojs开发抓取广告
通信从IM换成了 streamHttp

# 调研和CDD
 2025年5.15-6月 15天
1. 系统调研了有米的财报
2. 做了广告DAAS的 CDD大纲 
3. 研究 产品原型

结论是: TPF换了两个方向花了105天。
       调研生意15天。
       产品未上线。
       120天后决策全面做出海产品,放弃做国内产品
       放弃原因：1.国内数据抓取合规性
                2.获客成本到1万/客 非常高


# 产品2 - 出海云真机 51天
为什么做产品2
原始需求是 基于魔云腾ARM批量注册海外APP账号,批量养号
分析需求的时候 
1. 出海手机  2. Esim卡  3. 代理IP
##
1+2+3            是真机的多账号管理 对外做产品
魔云腾ARM+2+3    是海量账号的方案 对内做基建

因为判断是技术上可以服用，就先做了云真机的产品

2025年6月-7月21 出海云真机产品
策略：CDD内容低成本验证,汲取产品1的教训。没写代码，先验证需求
内容4篇 151 84 69 53 总阅读357
转化
SIM群10人
IP群10人
出海产品个微1人
结论 从内容阅读量和引入用户来看，
出海云真机的PMF没有跑通。


# 认知
还有一个关键思考   在产品1 - 广有财DASS的时候 我会后端开发 但并不会安卓端开发 xposed逆向 autojs   因为这些关键的技术不会导致我把TPF的部分投入最多？  在产品2的时候 我已经能确定产品2 我一定可以做出来了 所以我可以先做CDD营销内容？

1个产品应该按照2个月上线倒着拆

 TOC业务一定会遇到真实的问题    通过大模型来做产品原型和CDD内容 成本极大的降低了。 让我可以同时做TOB的事情

# 下一步决策思考 2025.7.27
可选项目:

海外工具站
卖号站
Automa营销工作流



# 产品3 - ?
