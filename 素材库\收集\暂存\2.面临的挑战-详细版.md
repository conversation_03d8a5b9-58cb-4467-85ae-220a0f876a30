# 创业路上的挑战

在打工的时候, 不需要考虑需求的商业价值。老板已经充满激情的确定了方向，产品经理会拿着详细的PRD,设计,后端,测试都有，拉一个评审会就能开干。
当作为独立开发者的的时候，情况开始不一样了！
- 我遇到的问题，别人是否也有同样的痛点？
- 这个需求是刚需还是可有可无？
- 这确实是一个需求,也有痛点,用户愿意为这个痛点买单吗？

在公司里，一个小SAAS项目的研发投入是这样的。
团队配置  1个产品 ,2个后端，1个前端，1个测试。公司会给2个月的时间把MVP做出来,MVP的成本大概在20万。2个月上线后,销售来导入用户测试，测2个月，看投产能否打整，这里大概也需要20万的成本。再加上其他有的没的费用，一个小SASS的成本在50万左右。

作为独立开发，一个人挑战50万的成本，内心还是慌的一批。

要么能承担极大风险，要么有牛逼的生产力。什么叫承担极大风险，逻辑上只要花20万 一定能把MVP做出来，能跑正就能赚钱了。纠结啥呢，不还是承担风险的水平不行。

有希望的是，还可以尝试牛逼生产力的路径，就是不能承担那么多风险，那就把MVP的成本降一降，把获客的成本降一降。比如50万降低到5万。

50万降低到5万。相对于作为独立开发者，要自己发现需求 → 自己评估可行性 → 自己设计原型 → 自己开发 → 自己测试 → 自己部署 → 自己推广 → 自己维护。 听起来头更大。

好在，现在大模型技术日新月异，有可能能在MVP环节大幅度降低成本。

我觉得还是要试一吧。
