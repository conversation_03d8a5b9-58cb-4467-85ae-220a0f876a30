基本功9：建飞轮|增长飞轮  

提到增长飞轮，最经典的就是亚马逊增长飞轮  

# 最佳实践： 经典亚马逊增长飞轮  

一堂重绘版·2022，《重新理解“增长飞轮”》  

![](images/e5f091e21faf5d7f167df8295651215243437633bb65bff45044631539fa9fa5.jpg)  

贝索斯把亚马逊的增长策略，画成了一个圈，形成了一个环，把这个叫做飞轮。这个飞轮20多年，一直在滚动、在迭代、造就了一个亚马逊的巨大商业帝国。  

其实增长飞轮”这个模型，已经有很多优秀的友商拆解过了。很多同行都在讲一些大公司的案例，更多侧重思维”。但是我们更希望给大家一套可以拿来就用"的实操方法。  

创业任务：有能力开始构建两类飞轮：简单的小飞轮，复杂的公司战略大飞轮。  

训练工具：  


<html><body><table><tr><td>工具</td><td>类型</td><td>训练目的</td></tr><tr><td>增长飞轮</td><td>方法模型（Framework）</td><td>理解飞轮效应构建飞轮思维</td></tr><tr><td>小飞轮三段论</td><td>方法模型（Framework）</td><td>学会构建自己的小飞轮</td></tr><tr><td>飞轮构建四步法</td><td>方法模型（Framework）</td><td>试着构建公司的战略飞轮</td></tr></table></body></html>  

# 模型：增长飞轮  

关于增长飞轮，同行讲了很多很深度的分析，这里来讲讲一堂的理解。  

增长飞轮的本质，是把一堆增长要素的因果关系，串起来，形成一个增强回路”。  

举个例子帮你理解。随着增长，你的业务可能会有很多要素发生变化：有更多的用户（C）更多的客户（B）、更多的日活、更多的毛利、更快的配送速度、更便宜的价格等等。  

这些要素之间有些什么内在联系呢？你会如何理解这些要素呢？常见的有三种思考方式  

# 你是如何理解关键要素的？  

![](images/ba006026726cd1fa03fa686105f5073794a58613a488db0554233e08501fe87b.jpg)  

第一类：散点思考。对于业务难题，全是一个个独立问题，哪儿出问题，去哪儿救火。  
只看局部，没有因果，没有整体。  

第二类：申行思考。常常可以把业务拆解成一个漏斗或者公式，能够站在体系的角度思考问题。  

第三类：飞轮思考。尝试把各个要素形成一个闭环，找到因果关系，互为促进，持续加强，形成一个增强回路""  

这三种思考方式一对比，就不难发现其中的区别以及飞轮的优势。  

# 94|以太一堂·内部训练手册  

# 模型：小飞轮三段论  

拆解像亚马逊那样的商业战略飞轮很难，我们可以先试着拆解一些简单的飞轮：  

1.关于个人学习（如提升认知段位）  

2.关于某一项能力提升（如提升拍短视频&学习投资）  

3.持续做某一个小的产品/项目（如写一本书&讲拆书会）  

这类飞轮比较简单，不涉及到公司的经营，不涉及到商业战略，不涉及到用户价值洞察，不涉及到多个业务板块，为了方便记忆，我们把这类简单的飞轮叫做“小飞轮”。  

具体如何构建简单的小飞轮呢，重点是要围绕你的核心卡点”进行。为此我们总结了小飞轮三段论：找卡点，加环节，做闭环。  

用一个简单的例子带你理解。比如，有很多人平时持续写作，非常辛苦，大致的流程是：想选题、写文章、发出去。  

第一步，找卡点。你要分析，你最难受的环节是什么，你卡在哪个环节：缺选题和素材、缺成就感、缺金钱回报。  

第二步，加环节。找到你的卡点之后，针对你的卡点，有哪些环节可以帮你解决这个难题。比如你卡在没选题素材。调研后你发现很多畅销书作者或者持续写作选手，会用用户投票选题、引导评论区留言、用户采访等等方法收集素材。  

第三步，做闭环。发现这些环节后，你就可以试着写出你的飞轮了。更多选题-更好写作-发布-鼓励用户参与互动-分析用户案例-更多选题。  
当然飞轮不是想出来的，需要大胆假设，小心求证”，一旦这个闭环真正形成，你的工作就会越来越简单。  

# 模型：飞轮构建四步法  

关于增长飞轮，市场上都在讲Why，都在讲飞轮有多厉害，有多神奇，但是讲How的极少，那么如何真正在自己的业务上思考和构建飞轮呢？  

我们结合吉姆·柯林斯的《飞轮效应》这本书和一堂的研究，做了简化，总结一个简版的增长飞轮构建方法：列要素、找因果、测闭环、狂拉动。  

第一步列要素：找出飞轮的要素。需要列出来一些飞轮的要素，常见的要素包含：  

·用户：用户，客户，更多APP下载，更多Web 流量，更多私域粉丝·价值：更好体验，多、快、好、省、更好的课程、更好的AI算法。·商业：更低成本结构，更高毛利，更多收入，更多利润，更高效率。·壁垒：品牌/声誉，更大规模，更强技术团队，更强粘性。  

第二步找因果：整理构件逻辑关系。需要找到所有关键的因果链，作为你的假设，要勇敢提出来。  

常见话术其实就是两条：这个会拉动什么？（看下一步）这个依赖什么？（看上一步）  

第三步测闭环：画成闭环，并测试。尝试找到4-6个关键的要素，把它首尾相连，画一版，形成一个增强回路。  

·少于4个：过于简单，往往无法反映业务关键问题·超过6个：过于复杂，很容易断掉，也容易忽略本质这一步的难点是形成一个闭环，需要你不断思考和验证。  

第四步狂拉动：如果跑通疯狂拉动。这一步的难点是你需要笃定并长期坚持。如果初步跑通，就要保持专注，心无旁骛在这个飞轮上，让他越滚越快。在非核心路径上，有些钱可以不挣，果断舍弃。  

# 96以太一堂·内部训练手册