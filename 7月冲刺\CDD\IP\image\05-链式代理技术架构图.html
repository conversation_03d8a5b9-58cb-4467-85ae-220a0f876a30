<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>链式代理技术架构图</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .title {
            text-align: center;
            font-size: 28px;
            color: #2c3e50;
            margin-bottom: 40px;
            font-weight: bold;
        }
        
        .architecture-diagram {
            margin: 0 auto;
            max-width: 900px;
            position: relative;
        }
        
        .diagram-container {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }
        
        .diagram-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
        }
        
        .node {
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            min-width: 150px;
            position: relative;
            z-index: 2;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        
        .device-node {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
        }
        
        .transit-node {
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
            color: white;
        }
        
        .proxy-node {
            background: linear-gradient(135deg, #45b7d1, #96c93d);
            color: white;
        }
        
        .target-node {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        
        .node-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .node-desc {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .node-flag {
            font-size: 24px;
            margin-top: 10px;
        }
        
        .node-stats {
            background: rgba(255,255,255,0.2);
            border-radius: 5px;
            padding: 8px;
            margin-top: 10px;
            font-size: 12px;
        }
        
        .arrow-container {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            z-index: 1;
        }
        
        .arrow {
            height: 4px;
            background: #3498db;
            width: 100%;
            position: relative;
        }
        
        .arrow::after {
            content: '';
            position: absolute;
            right: 0;
            top: -8px;
            border-left: 15px solid #3498db;
            border-top: 10px solid transparent;
            border-bottom: 10px solid transparent;
        }
        
        .arrow-label {
            position: absolute;
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
            background: #3498db;
            color: white;
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 12px;
            white-space: nowrap;
        }

        .ip-pool {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
            justify-content: center;
            margin-top: 8px;
        }

        .ip-item {
            background: rgba(255,255,255,0.3);
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            border: 1px solid rgba(255,255,255,0.5);
        }

        .ip-dots {
            color: rgba(255,255,255,0.8);
            font-size: 12px;
            align-self: center;
        }

        .multi-arrow {
            position: relative;
            width: 100%;
            height: 20px;
        }

        .arrow-line {
            position: absolute;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            border-radius: 2px;
        }

        .arrow-line::after {
            content: '';
            position: absolute;
            right: 0;
            top: -6px;
            border-left: 12px solid #2ecc71;
            border-top: 8px solid transparent;
            border-bottom: 8px solid transparent;
        }
        
        .component-details {
            margin-top: 60px;
        }
        
        .component-title {
            font-size: 22px;
            color: #2c3e50;
            margin-bottom: 20px;
            font-weight: bold;
            text-align: center;
        }
        
        .components-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }
        
        .component-box {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }
        
        .component-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .component-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            margin-right: 15px;
            color: white;
        }
        
        .transit-icon {
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
        }
        
        .proxy-icon {
            background: linear-gradient(135deg, #45b7d1, #96c93d);
        }
        
        .component-name {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .component-desc {
            color: #7f8c8d;
            font-size: 14px;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        
        .options-list {
            background: white;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #ecf0f1;
        }
        
        .option-item {
            margin-bottom: 12px;
            padding-bottom: 12px;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .option-item:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }
        
        .option-name {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .option-desc {
            color: #7f8c8d;
            font-size: 13px;
        }
        
        .technical-notes {
            margin-top: 60px;
            background: #ecf0f1;
            border-radius: 10px;
            padding: 25px;
        }
        
        .notes-title {
            font-size: 20px;
            color: #2c3e50;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .notes-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
        }
        
        .note-box {
            background: white;
            border-radius: 8px;
            padding: 15px;
            border-left: 4px solid #3498db;
        }
        
        .note-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 8px;
        }
        
        .note-desc {
            color: #7f8c8d;
            font-size: 13px;
        }
        
        .emoji {
            font-size: 18px;
            margin-right: 5px;
        }

        .account-mapping {
            margin-top: 60px;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
        }

        .mapping-title {
            font-size: 20px;
            color: #2c3e50;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: center;
        }

        .mapping-container {
            display: grid;
            grid-template-columns: 1fr auto 1fr;
            gap: 20px;
            align-items: start;
        }

        .accounts-column, .ips-column {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }

        .column-title {
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            text-align: center;
            border-bottom: 2px solid #3498db;
            padding-bottom: 8px;
        }

        .account-item, .ip-mapping-item {
            background: #f8f9fa;
            padding: 8px 12px;
            margin-bottom: 8px;
            border-radius: 5px;
            font-size: 14px;
            text-align: center;
            border-left: 3px solid #3498db;
        }

        .account-dots, .mapping-dots {
            text-align: center;
            color: #7f8c8d;
            font-size: 18px;
            margin: 10px 0;
        }

        .mapping-arrows {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-around;
            height: 100%;
            padding: 20px 0;
        }

        .mapping-arrow {
            font-size: 20px;
            color: #3498db;
            font-weight: bold;
        }

        .mapping-note {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin-top: 20px;
            border-radius: 5px;
            font-size: 14px;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="title">链式代理技术架构图</div>
        
        <div class="architecture-diagram">
            <div class="diagram-container">
                <div class="diagram-row">
                    <div class="node device-node">
                        <div class="node-title">你的设备</div>
                        <div class="node-desc">手机/电脑</div>
                        <div class="node-flag">🇨🇳</div>
                        <div class="node-stats">真实IP</div>
                    </div>
                    
                    <div class="arrow-container">
                        <div class="arrow">
                            <div class="arrow-label">第一跳: 50ms</div>
                        </div>
                    </div>
                    
                    <div class="node transit-node">
                        <div class="node-title">中转服务器</div>
                        <div class="node-desc">搬瓦工VPS</div>
                        <div class="node-flag">🇭🇰</div>
                        <div class="node-stats">2核2G内存</div>
                    </div>
                    
                    <div class="arrow-container">
                        <div class="multi-arrow">
                            <div class="arrow-line" style="top: -10px;"></div>
                            <div class="arrow-line" style="top: 0px;"></div>
                            <div class="arrow-line" style="top: 10px;"></div>
                            <div class="arrow-label">第二跳: 100ms<br><small>多账号分流</small></div>
                        </div>
                    </div>
                    
                    <div class="node proxy-node">
                        <div class="node-title">代理服务商</div>
                        <div class="node-desc">ClipProxy/911S5</div>
                        <div class="node-flag">🇺🇸</div>
                        <div class="node-stats">
                            <div class="ip-pool">
                                <div class="ip-item">IP-001</div>
                                <div class="ip-item">IP-002</div>
                                <div class="ip-item">IP-003</div>
                                <div class="ip-dots">⋮</div>
                                <div class="ip-item">IP-1000</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="arrow-container">
                        <div class="arrow">
                            <div class="arrow-label">第三跳: 20ms</div>
                        </div>
                    </div>
                    
                    <div class="node target-node">
                        <div class="node-title">目标平台</div>
                        <div class="node-desc">TikTok/Facebook</div>
                        <div class="node-flag">🇺🇸</div>
                        <div class="node-stats">目标服务器</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="component-details">
            <div class="component-title">核心组件详解</div>
            
            <div class="components-grid">
                <div class="component-box">
                    <div class="component-header">
                        <div class="component-icon transit-icon">🌐</div>
                        <div class="component-name">中转网络</div>
                    </div>
                    <div class="component-desc">
                        中转网络是链式代理的第一跳，负责将你的流量从中国转发到海外，解决代理服务商不接受中国IP直连的问题。
                    </div>
                    <div class="options-list">
                        <div class="option-item">
                            <div class="option-name">搬瓦工VPS</div>
                            <div class="option-desc">香港/新加坡节点，2核2G配置，$20-50/月，稳定可靠</div>
                        </div>
                        <div class="option-item">
                            <div class="option-name">JustMySocks</div>
                            <div class="option-desc">现成的中转服务，无需自己配置，$5.88/月起</div>
                        </div>
                        <div class="option-item">
                            <div class="option-name">Vultr/DigitalOcean</div>
                            <div class="option-desc">亚太地区节点，配置灵活，按小时计费</div>
                        </div>
                    </div>
                </div>
                
                <div class="component-box">
                    <div class="component-header">
                        <div class="component-icon proxy-icon">🔄</div>
                        <div class="component-name">落地网络</div>
                    </div>
                    <div class="component-desc">
                        落地网络是链式代理的第二跳，提供大量干净的IP资源，让目标平台看到的是美国/欧洲等地的IP地址。
                    </div>
                    <div class="options-list">
                        <div class="option-item">
                            <div class="option-name">ClipProxy</div>
                            <div class="option-desc">企业级代理服务，IP质量高，稳定性好，价格较高</div>
                        </div>
                        <div class="option-item">
                            <div class="option-name">911S5</div>
                            <div class="option-desc">IP池庞大，覆盖全球，价格适中，适合大规模需求</div>
                        </div>
                        <div class="option-item">
                            <div class="option-name">IPIPGo</div>
                            <div class="option-desc">住宅IP代理，IP质量好，适合对IP质量要求高的场景</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="account-mapping">
            <div class="mapping-title">📱 账号与IP对应关系</div>
            <div class="mapping-container">
                <div class="accounts-column">
                    <div class="column-title">TikTok账号</div>
                    <div class="account-item">账号001</div>
                    <div class="account-item">账号002</div>
                    <div class="account-item">账号003</div>
                    <div class="account-dots">⋮</div>
                    <div class="account-item">账号1000</div>
                </div>

                <div class="mapping-arrows">
                    <div class="mapping-arrow">→</div>
                    <div class="mapping-arrow">→</div>
                    <div class="mapping-arrow">→</div>
                    <div class="mapping-dots">⋮</div>
                    <div class="mapping-arrow">→</div>
                </div>

                <div class="ips-column">
                    <div class="column-title">独立住宅IP</div>
                    <div class="ip-mapping-item">*********** 🇺🇸</div>
                    <div class="ip-mapping-item">*********** 🇺🇸</div>
                    <div class="ip-mapping-item">*********** 🇺🇸</div>
                    <div class="mapping-dots">⋮</div>
                    <div class="ip-mapping-item">***********000 🇺🇸</div>
                </div>
            </div>
            <div class="mapping-note">
                <strong>核心原理：</strong>每个TikTok账号都通过中转服务器连接到一个独立的美国住宅IP，<br>
                确保账号之间完全隔离，避免连坐风险。1台中转服务器可以管理1000个账号的流量分发。
            </div>
        </div>

        <div class="technical-notes">
            <div class="notes-title">技术实现要点</div>
            
            <div class="notes-grid">
                <div class="note-box">
                    <div class="note-title">
                        <span class="emoji">🔒</span>协议选择
                    </div>
                    <div class="note-desc">
                        中转服务器通常使用V2Ray/Trojan等协议，落地网络多采用SOCKS5/HTTP代理协议，需要确保协议兼容
                    </div>
                </div>
                
                <div class="note-box">
                    <div class="note-title">
                        <span class="emoji">⚡</span>性能优化
                    </div>
                    <div class="note-desc">
                        中转服务器需开启BBR等TCP加速技术，合理设置并发连接数和超时时间，避免资源耗尽
                    </div>
                </div>
                
                <div class="note-box">
                    <div class="note-title">
                        <span class="emoji">🔄</span>负载均衡
                    </div>
                    <div class="note-desc">
                        当账号数量较多时，可配置多台中转服务器，通过负载均衡分散流量，提高整体稳定性
                    </div>
                </div>
                
                <div class="note-box">
                    <div class="note-title">
                        <span class="emoji">📊</span>监控系统
                    </div>
                    <div class="note-desc">
                        建立完善的监控系统，实时监测延迟、丢包率、连接成功率等指标，及时发现并解决问题
                    </div>
                </div>
                
                <div class="note-box">
                    <div class="note-title">
                        <span class="emoji">🔍</span>IP轮换策略
                    </div>
                    <div class="note-desc">
                        根据平台风控规则，设置合理的IP轮换策略，避免单个IP使用频率过高导致风险
                    </div>
                </div>
                
                <div class="note-box">
                    <div class="note-title">
                        <span class="emoji">💾</span>数据备份
                    </div>
                    <div class="note-desc">
                        定期备份中转服务器配置，建立快照，确保系统出现问题时能快速恢复
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
