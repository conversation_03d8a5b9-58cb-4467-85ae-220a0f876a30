# 第2部分：MCP工具开发

## 🛠️ 开发环境准备

### 依赖安装
```bash
pip install mcp fastapi uvicorn psycopg2-binary redis python-multipart
```

### 项目结构
```
mcp-tools/
├── ad_data_query/
│   ├── __init__.py
│   ├── mcp_server.py
│   └── database.py
├── visualization/
│   ├── __init__.py
│   ├── mcp_server.py
│   └── generators.py
└── requirements.txt
```

## 🔍 AdDataQueryMCP 实现

### 核心服务器代码

```python
# ad_data_query/mcp_server.py
from mcp.server import Server
from mcp.types import Tool, TextContent
import json
import asyncio
from .database import AdDatabase

# 创建MCP服务器
server = Server("ad-data-query")

# 初始化数据库连接
db = AdDatabase()

@server.list_tools()
async def list_tools():
    """列出可用的工具"""
    return [
        Tool(
            name="query_ad_data",
            description="查询用户的个人广告数据",
            inputSchema={
                "type": "object",
                "properties": {
                    "user_id": {
                        "type": "string",
                        "description": "用户标识"
                    },
                    "date_range": {
                        "type": "string", 
                        "description": "时间范围，如'最近30天'、'2024年1月'"
                    },
                    "platform": {
                        "type": "string",
                        "description": "平台筛选：Facebook/TikTok/小红书/抖音/all",
                        "default": "all"
                    },
                    "keywords": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "关键词搜索列表"
                    },
                    "category": {
                        "type": "string",
                        "description": "行业分类筛选"
                    },
                    "limit": {
                        "type": "integer",
                        "description": "返回数量限制",
                        "default": 100
                    }
                },
                "required": ["user_id"]
            }
        )
    ]

@server.call_tool()
async def call_tool(name: str, arguments: dict):
    """执行工具调用"""
    if name == "query_ad_data":
        try:
            # 解析参数
            user_id = arguments["user_id"]
            date_range = arguments.get("date_range", "最近30天")
            platform = arguments.get("platform", "all")
            keywords = arguments.get("keywords", [])
            category = arguments.get("category")
            limit = arguments.get("limit", 100)
            
            # 执行查询
            result = await db.query_ads({
                "user_id": user_id,
                "date_range": date_range,
                "platform": platform,
                "keywords": keywords,
                "category": category,
                "limit": limit
            })
            
            return [TextContent(
                type="text",
                text=json.dumps(result, ensure_ascii=False, indent=2)
            )]
            
        except Exception as e:
            return [TextContent(
                type="text", 
                text=f"查询失败: {str(e)}"
            )]
    
    raise ValueError(f"未知工具: {name}")

if __name__ == "__main__":
    import asyncio
    asyncio.run(server.run())
```

### 数据库访问层

```python
# ad_data_query/database.py
import asyncpg
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any

class AdDatabase:
    def __init__(self):
        self.connection_string = "postgresql://user:password@localhost/addb"
        self.pool = None
    
    async def init_pool(self):
        """初始化连接池"""
        if not self.pool:
            self.pool = await asyncpg.create_pool(self.connection_string)
    
    async def query_ads(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """查询广告数据"""
        await self.init_pool()
        
        # 构建SQL查询
        query, query_params = self._build_query(params)
        
        async with self.pool.acquire() as conn:
            # 执行查询
            rows = await conn.fetch(query, *query_params)
            
            # 处理结果
            ads = []
            for row in rows:
                ad = {
                    "id": row["id"],
                    "content": {
                        "text": row["ad_text"],
                        "images": json.loads(row["images"] or "[]"),
                        "videos": json.loads(row["videos"] or "[]")
                    },
                    "platform": row["platform"],
                    "timestamp": row["created_at"].isoformat(),
                    "user_interaction": {
                        "clicked": row["clicked"],
                        "saved": row["saved"],
                        "shared": row["shared"]
                    },
                    "raw_tags": json.loads(row["tags"] or "[]")
                }
                ads.append(ad)
            
            return {
                "total_count": len(ads),
                "ads": ads,
                "query_info": params
            }
    
    def _build_query(self, params: Dict[str, Any]) -> tuple:
        """构建SQL查询语句"""
        base_query = """
        SELECT id, ad_text, images, videos, platform, created_at,
               clicked, saved, shared, tags
        FROM user_ads 
        WHERE user_id = $1
        """
        
        query_params = [params["user_id"]]
        param_count = 1
        
        # 添加时间范围筛选
        if params.get("date_range"):
            date_filter, date_param = self._parse_date_range(params["date_range"])
            if date_filter:
                param_count += 1
                base_query += f" AND created_at >= ${param_count}"
                query_params.append(date_param)
        
        # 添加平台筛选
        if params.get("platform") and params["platform"] != "all":
            param_count += 1
            base_query += f" AND platform = ${param_count}"
            query_params.append(params["platform"])
        
        # 添加关键词搜索
        if params.get("keywords"):
            keyword_conditions = []
            for keyword in params["keywords"]:
                param_count += 1
                keyword_conditions.append(f"ad_text ILIKE ${param_count}")
                query_params.append(f"%{keyword}%")
            
            if keyword_conditions:
                base_query += " AND (" + " OR ".join(keyword_conditions) + ")"
        
        # 添加分类筛选
        if params.get("category"):
            param_count += 1
            base_query += f" AND tags::text ILIKE ${param_count}"
            query_params.append(f"%{params['category']}%")
        
        # 添加排序和限制
        base_query += " ORDER BY created_at DESC"
        if params.get("limit"):
            param_count += 1
            base_query += f" LIMIT ${param_count}"
            query_params.append(params["limit"])
        
        return base_query, query_params
    
    def _parse_date_range(self, date_range: str) -> tuple:
        """解析时间范围"""
        now = datetime.now()
        
        if "最近" in date_range:
            if "天" in date_range:
                days = int(''.join(filter(str.isdigit, date_range)))
                return True, now - timedelta(days=days)
            elif "月" in date_range:
                months = int(''.join(filter(str.isdigit, date_range)))
                return True, now - timedelta(days=months * 30)
        
        # 可以添加更多日期解析逻辑
        return False, None
```

## 📊 VisualizationMCP 实现

### 核心服务器代码

```python
# visualization/mcp_server.py
from mcp.server import Server
from mcp.types import Tool, TextContent
import json
from .generators import ChartGenerator, PageGenerator

server = Server("visualization")

# 初始化生成器
chart_gen = ChartGenerator()
page_gen = PageGenerator()

@server.list_tools()
async def list_tools():
    return [
        Tool(
            name="generate_visualization",
            description="生成数据可视化内容",
            inputSchema={
                "type": "object",
                "properties": {
                    "data": {
                        "type": "object",
                        "description": "待可视化的数据"
                    },
                    "viz_type": {
                        "type": "string",
                        "enum": ["trend_chart", "opportunity_dashboard", "comparison_chart", "category_distribution"],
                        "description": "可视化类型"
                    },
                    "title": {
                        "type": "string",
                        "description": "图表标题"
                    },
                    "interactive": {
                        "type": "boolean",
                        "description": "是否需要交互功能",
                        "default": True
                    }
                },
                "required": ["data", "viz_type"]
            }
        )
    ]

@server.call_tool()
async def call_tool(name: str, arguments: dict):
    if name == "generate_visualization":
        try:
            data = arguments["data"]
            viz_type = arguments["viz_type"]
            title = arguments.get("title", "数据分析")
            interactive = arguments.get("interactive", True)
            
            # 根据类型生成可视化
            if viz_type == "opportunity_dashboard":
                result = await page_gen.generate_opportunity_dashboard(
                    data, title, interactive
                )
            elif viz_type == "trend_chart":
                result = await chart_gen.generate_trend_chart(
                    data, title, interactive
                )
            elif viz_type == "comparison_chart":
                result = await chart_gen.generate_comparison_chart(
                    data, title, interactive
                )
            else:
                result = await chart_gen.generate_generic_chart(
                    data, viz_type, title, interactive
                )
            
            return [TextContent(
                type="text",
                text=json.dumps(result, ensure_ascii=False, indent=2)
            )]
            
        except Exception as e:
            return [TextContent(
                type="text",
                text=f"可视化生成失败: {str(e)}"
            )]
    
    raise ValueError(f"未知工具: {name}")
```

### 可视化生成器

```python
# visualization/generators.py
import json
import uuid
from datetime import datetime
from typing import Dict, Any

class ChartGenerator:
    def __init__(self):
        self.base_url = "http://localhost:8000/static"
    
    async def generate_trend_chart(self, data: Dict, title: str, interactive: bool) -> Dict[str, Any]:
        """生成趋势分析图"""
        
        # 生成Chart.js配置
        chart_config = {
            "type": "line",
            "data": {
                "labels": data.get("time_labels", []),
                "datasets": [{
                    "label": "广告数量趋势",
                    "data": data.get("trend_data", []),
                    "borderColor": "rgb(75, 192, 192)",
                    "backgroundColor": "rgba(75, 192, 192, 0.2)",
                    "tension": 0.1
                }]
            },
            "options": {
                "responsive": True,
                "plugins": {
                    "title": {
                        "display": True,
                        "text": title
                    }
                },
                "scales": {
                    "y": {
                        "beginAtZero": True
                    }
                }
            }
        }
        
        # 生成HTML页面
        html_content = self._generate_chart_html(chart_config, title)
        
        # 保存文件
        file_id = str(uuid.uuid4())
        file_path = f"charts/{file_id}.html"
        
        # 这里应该保存到实际的文件系统
        # await self._save_file(file_path, html_content)
        
        return {
            "html_url": f"{self.base_url}/{file_path}",
            "preview_image": f"{self.base_url}/previews/{file_id}.png",
            "interactive_features": ["zoom", "pan", "hover"] if interactive else [],
            "chart_type": "trend_chart"
        }
    
    def _generate_chart_html(self, chart_config: Dict, title: str) -> str:
        """生成图表HTML页面"""
        return f"""
<!DOCTYPE html>
<html>
<head>
    <title>{title}</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .chart-container {{ width: 100%; height: 400px; }}
    </style>
</head>
<body>
    <h1>{title}</h1>
    <div class="chart-container">
        <canvas id="chart"></canvas>
    </div>
    <script>
        const ctx = document.getElementById('chart').getContext('2d');
        new Chart(ctx, {json.dumps(chart_config)});
    </script>
</body>
</html>
        """

class PageGenerator:
    def __init__(self):
        self.base_url = "http://localhost:8000/static"
    
    async def generate_opportunity_dashboard(self, data: Dict, title: str, interactive: bool) -> Dict[str, Any]:
        """生成机会分析仪表板"""
        
        opportunities = data.get("opportunities", [])
        
        # 生成仪表板HTML
        html_content = self._generate_dashboard_html(opportunities, title)
        
        # 保存文件
        file_id = str(uuid.uuid4())
        file_path = f"dashboards/{file_id}.html"
        
        return {
            "html_url": f"{self.base_url}/{file_path}",
            "preview_image": f"{self.base_url}/previews/{file_id}.png",
            "interactive_features": ["filter", "sort", "export"] if interactive else [],
            "chart_type": "opportunity_dashboard"
        }
    
    def _generate_dashboard_html(self, opportunities: list, title: str) -> str:
        """生成仪表板HTML"""
        
        opportunity_cards = ""
        for i, opp in enumerate(opportunities):
            opportunity_cards += f"""
            <div class="opportunity-card">
                <h3>🔥 机会{i+1}：{opp.get('name', '未知机会')}</h3>
                <p><strong>出现频次：</strong>{opp.get('frequency', 0)}次</p>
                <p><strong>增长率：</strong>{opp.get('growth', 0)}%</p>
                <p><strong>主要玩家：</strong>{', '.join(opp.get('players', []))}</p>
                <p><strong>机会分析：</strong>{opp.get('analysis', '暂无分析')}</p>
            </div>
            """
        
        return f"""
<!DOCTYPE html>
<html>
<head>
    <title>{title}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }}
        .dashboard {{ max-width: 1200px; margin: 0 auto; }}
        .opportunity-card {{ 
            background: white; 
            margin: 20px 0; 
            padding: 20px; 
            border-radius: 8px; 
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        h1 {{ color: #333; text-align: center; }}
        h3 {{ color: #e74c3c; margin-top: 0; }}
    </style>
</head>
<body>
    <div class="dashboard">
        <h1>{title}</h1>
        {opportunity_cards}
    </div>
</body>
</html>
        """
```

## 🚀 部署和测试

### 启动MCP服务器

```bash
# 启动广告数据查询服务
cd ad_data_query
python mcp_server.py

# 启动可视化服务  
cd visualization
python mcp_server.py
```

### 测试工具调用

```python
# 测试脚本
import asyncio
import json
from mcp.client import Client

async def test_tools():
    # 连接到MCP服务器
    ad_client = Client("stdio", ["python", "ad_data_query/mcp_server.py"])
    viz_client = Client("stdio", ["python", "visualization/mcp_server.py"])
    
    # 测试广告数据查询
    ad_result = await ad_client.call_tool("query_ad_data", {
        "user_id": "test_user",
        "date_range": "最近30天",
        "keywords": ["AI工具"]
    })
    
    print("广告数据查询结果：")
    print(ad_result)
    
    # 测试可视化生成
    viz_result = await viz_client.call_tool("generate_visualization", {
        "data": {"opportunities": [{"name": "AI写作工具", "frequency": 15, "growth": 300}]},
        "viz_type": "opportunity_dashboard",
        "title": "商业机会分析"
    })
    
    print("可视化生成结果：")
    print(viz_result)

if __name__ == "__main__":
    asyncio.run(test_tools())
```

## 📝 开发注意事项

1. **错误处理**：确保所有异常都被正确捕获和返回
2. **数据验证**：验证输入参数的有效性
3. **性能优化**：对于大量数据查询要考虑分页和缓存
4. **安全性**：防止SQL注入和其他安全问题
5. **日志记录**：记录工具调用和错误信息便于调试

这样你就有了两个功能完整的MCP工具，专注于数据获取和结果展示，为上层的AI分析提供支持。
