<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>出海三件套最后一环：为什么IP这么复杂？</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            min-height: 100vh;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }
        
        .main-title {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 18px;
            opacity: 0.9;
            margin-bottom: 30px;
        }
        
        .author-info {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            display: inline-block;
        }
        
        .nav-menu {
            background: #34495e;
            padding: 20px 30px;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .nav-list {
            list-style: none;
            display: flex;
            gap: 30px;
            flex-wrap: wrap;
        }
        
        .nav-item {
            color: white;
            text-decoration: none;
            padding: 8px 15px;
            border-radius: 5px;
            transition: background-color 0.3s ease;
            font-size: 14px;
        }
        
        .nav-item:hover {
            background-color: rgba(255,255,255,0.2);
        }
        
        .content {
            padding: 40px 30px;
        }
        
        .section {
            margin-bottom: 60px;
        }
        
        .section-title {
            font-size: 28px;
            color: #2c3e50;
            margin-bottom: 30px;
            padding-bottom: 10px;
            border-bottom: 3px solid #3498db;
            font-weight: bold;
        }
        
        .section-subtitle {
            font-size: 22px;
            color: #34495e;
            margin: 30px 0 20px 0;
            font-weight: bold;
        }
        
        .paragraph {
            font-size: 16px;
            line-height: 1.8;
            margin-bottom: 20px;
            color: #444;
        }
        
        .highlight {
            background: #fff3cd;
            padding: 2px 6px;
            border-radius: 3px;
            color: #856404;
            font-weight: bold;
        }
        
        .quote {
            background: #f8f9fa;
            border-left: 4px solid #3498db;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
            font-style: italic;
        }
        
        .chart-container {
            margin: 30px 0;
            text-align: center;
        }
        
        .chart-iframe {
            width: 100%;
            height: 600px;
            border: none;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .chart-caption {
            margin-top: 15px;
            font-size: 14px;
            color: #7f8c8d;
            font-style: italic;
        }
        
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 20px 0;
            overflow-x: auto;
            white-space: pre;
        }
        
        .list {
            margin: 20px 0;
            padding-left: 20px;
        }
        
        .list-item {
            margin-bottom: 10px;
            font-size: 16px;
            line-height: 1.6;
        }
        
        .emphasis {
            font-weight: bold;
            color: #2c3e50;
        }
        
        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: #3498db;
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
            transition: all 0.3s ease;
            opacity: 0;
            visibility: hidden;
        }
        
        .back-to-top.visible {
            opacity: 1;
            visibility: visible;
        }
        
        .back-to-top:hover {
            background: #2980b9;
            transform: translateY(-3px);
        }
        
        .footer {
            background: #2c3e50;
            color: white;
            padding: 40px 30px;
            text-align: center;
        }

        .footer-content {
            max-width: 800px;
            margin: 0 auto;
            text-align: left;
        }

        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 3px;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            z-index: 1000;
            transition: width 0.3s ease;
        }

        .section {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }

        .section.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .chart-container {
            opacity: 0;
            transform: scale(0.95);
            transition: all 0.8s ease;
        }

        .chart-container.visible {
            opacity: 1;
            transform: scale(1);
        }
        
        @media (max-width: 768px) {
            .main-title {
                font-size: 28px;
            }
            
            .nav-list {
                flex-direction: column;
                gap: 10px;
            }
            
            .content {
                padding: 20px 15px;
            }
            
            .section-title {
                font-size: 24px;
            }
            
            .chart-iframe {
                height: 400px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1 class="main-title">出海三件套最后一环：为什么IP这么复杂？</h1>
            <p class="subtitle">从10年前网游加速器的经历说起，彻底讲清楚IP这件事的本质</p>
            <div class="author-info">
                <p>基于真实经验的深度技术分析</p>
            </div>
        </header>
        
        <nav class="nav-menu">
            <div class="nav-list">
                <a href="#intro" class="nav-item">开篇</a>
                <a href="#gaming-history" class="nav-item">网游加速器历史</a>
                <a href="#three-problems" class="nav-item">3个核心问题</a>
                <a href="#scenario-comparison" class="nav-item">场景对比</a>
                <a href="#server-problem" class="nav-item">1000台服务器问题</a>
                <a href="#proxy-solution" class="nav-item">代理服务商</a>
                <a href="#chain-proxy" class="nav-item">链式代理</a>
                <a href="#final-solution" class="nav-item">完整方案</a>
            </div>
        </nav>
        
        <main class="content">
            <section id="intro" class="section">
                <h2 class="section-title">开篇：IP的复杂性</h2>

                <p class="paragraph">
                    出海三件套：手机、卡、IP。
                </p>

                <p class="paragraph">
                    水最深的就是IP了吧。
                </p>

                <p class="paragraph">
                    0播放了，掉橱窗了，什么问题都可以让IP背下锅。同时市面上又有各种各样的解决方案：小火箭、软路由、SDWAN、专线、机房IP、住宅IP...
                </p>

                <p class="paragraph">
                    价格也是个谜，从一个月10几块到一个月1000-2000。价格跨度巨大，对于小白来说，肉眼看起来好像又没有什么区别。
                </p>

                <div class="quote">
                    今天我就从10年前网游加速器的经历说起，彻底讲清楚IP这件事的本质。
                </div>
            </section>

            <section id="gaming-history" class="section">
                <h2 class="section-title">网游加速器的那些年</h2>

                <p class="paragraph">
                    2014-2015年，我加入了一家网游加速器公司，当时是TOP3的加速器。我负责搞流量。
                </p>

                <p class="paragraph">
                    网游加速器这个产品是怎么来的？
                </p>

                <p class="paragraph">
                    就不得不提英雄联盟和暗黑破坏神3这两个标志性的游戏了。我隐约还记得，这两款游戏的营收占了整个公司大盘的80%以上。
                </p>

                <h3 class="section-subtitle">英雄联盟的诱惑</h3>

                <p class="paragraph">
                    2014-2015年，韩服（KR）是公认的LOL"圣地"，职业选手和顶尖路人云集。去韩服打Rank是证明自己、提升技术的最佳途径。
                </p>

                <p class="paragraph">
                    风气更好，普遍认为外服（尤其是韩服）的游戏环境更纯粹，玩家求胜欲强，很少有国服当时常见的"演员"和"20投"现象。
                </p>

                <p class="paragraph">
                    当时游戏直播刚刚兴起，大主播们"征战韩服"是重要的直播内容，吸引了大量粉丝模仿，也想去体验一下。
                </p>

                <h3 class="section-subtitle">暗黑破坏神3的无奈</h3>

                <p class="paragraph">
                    《暗黑破坏神3》全球发售是2012年，资料片《夺魂之镰》是2014年3月上线。但国服直到2015年4月才正式公测。
                </p>

                <p class="paragraph">
                    在这之前，中国玩家想玩这款大作，唯一途径就是去玩亚服、美服或欧服。暗黑3的赛季模式是核心玩法，全球同步开启。想和全球玩家一起"开荒"，就必须去外服。
                </p>
            </section>

            <section id="three-problems" class="section">
                <h2 class="section-title">网游加速器核心解决3个问题</h2>

                <div class="chart-container">
                    <iframe src="../image/01-三个问题框架图.html" class="chart-iframe"></iframe>
                    <p class="chart-caption">网游加速器解决的3个核心问题框架图</p>
                </div>

                <ul class="list">
                    <li class="list-item"><span class="emphasis">IP地理限制</span> - 不能登录美服、韩服游戏</li>
                    <li class="list-item"><span class="emphasis">网络速度慢</span> - 延迟高经常丢包</li>
                    <li class="list-item"><span class="emphasis">IP质量问题</span> - 会被游戏服务器封号</li>
                </ul>

                <h3 class="section-subtitle">10年后的重新思考</h3>

                <p class="paragraph">
                    10年前我主要搞流量。后来做了运营，干了电商，成了一个全栈开发。
                </p>

                <p class="paragraph">
                    最近和当年的技术团队再次沟通，聊了聊网游加速器的技术实现。有了技术背景，沟通起来就顺畅得多。猛然想起当年开会讨论的技术问题，现在竟然又能理解了。
                </p>

                <p class="paragraph">
                    聊完之后也交叉验证了一个发现：网游加速器解决的问题，和现在出海矩阵或者出海私域遇到的问题，<span class="highlight">本质上是一样的，但侧重点又不太一样</span>。
                </p>

                <h3 class="section-subtitle">拆解3个核心问题</h3>

                <h4 class="section-subtitle">问题1：IP地理限制</h4>

                <p class="paragraph">
                    <span class="emphasis">本质：</span> 平台检测IP地理位置，限制中国用户
                </p>

                <p class="paragraph">
                    <span class="emphasis">解决方案：</span> 一个美国VPS就够了
                </p>

                <div class="code-block">┌─────────┐      ┌─────────┐      ┌─────────┐
│  你的   │      │  美国   │      │ TikTok  │
│  设备   │─────>│  VPS    │─────>│ 服务器  │
└─────────┘      └─────────┘      └─────────┘
    🇨🇳              🇺🇸              🇺🇸</div>

                <p class="paragraph">
                    <span class="emphasis">工作原理：</span>
                </p>
                <ul class="list">
                    <li class="list-item">TikTok看到的是美国IP，不是中国IP</li>
                    <li class="list-item">绕过了地理限制，可以正常访问</li>
                    <li class="list-item">但这个只是能登录了，体验嘛...就一般般</li>
                </ul>

                <h4 class="section-subtitle">问题2：网络速度</h4>

                <p class="paragraph">
                    <span class="emphasis">本质：</span> 直连延迟高、不稳定
                </p>

                <p class="paragraph">
                    现实的物理和网络障碍是巨大的。不使用加速器，直连外服会遇到以下致命问题：
                </p>

                <p class="paragraph">
                    <span class="emphasis">物理距离导致的硬伤：高延迟</span>
                </p>

                <p class="paragraph">
                    数据传输速度受光速限制。从中国到美国/欧洲的游戏服务器，数据一来一回，天然就会产生150ms-300ms的延迟。
                </p>

                <p class="paragraph">
                    这是什么概念？
                </p>
                <ul class="list">
                    <li class="list-item">LOL里，你的技能会慢0.2秒放出，你看到的敌人位置其实是0.2秒前的，你永远躲不掉关键技能，体验极差</li>
                    <li class="list-item">暗黑3里，尤其是在专家模式（Hardcore），一个延迟就可能让你反应不及，导致角色永久死亡，所有心血付诸东流</li>
                </ul>

                <p class="paragraph">
                    <span class="emphasis">跨国公网的顽疾：严重丢包</span>
                </p>

                <p class="paragraph">
                    你的游戏数据在去往国外服务器的路上，要经过无数个公共网络节点，尤其要挤过那个拥堵不堪的"国际出口网关"。
                </p>

                <p class="paragraph">
                    这就好比高峰期开车，不仅开得慢（高延迟），还随时可能因为堵死而"丢车"（丢包）。
                </p>

                <p class="paragraph">
                    游戏里丢包的表现就是：人物瞬移、卡在原地不动、技能按了没反应、突然掉线。这比稳定的高延迟更让人抓狂。
                </p>

                <div class="chart-container">
                    <iframe src="../image/02-网络架构对比图.html" class="chart-iframe"></iframe>
                    <p class="chart-caption">直连 vs 链式代理的网络架构对比</p>
                </div>

                <h4 class="section-subtitle">问题3：IP质量问题</h4>

                <p class="paragraph">
                    大部分网游加速器公司都是买几十个IP，所有玩这个游戏的玩家共用这几十个IP。很长一段时间都没什么问题。
                </p>

                <p class="paragraph">
                    我隐约还记得，有一段时间，某几个游戏对IP的要求特别高，导致用这个IP池的账号开始封号。也不是全封，有一定比例的账号会被封。大概率是某个玩家用了外挂，导致这个IP下的所有游戏账号被连坐。
                </p>

                <p class="paragraph">
                    后来的解决方案是提供独立IP。独立IP就是只给一个玩家用，就不会出现连坐封号的现象了。当然了，要加钱。
                </p>
            </section>

            <section id="scenario-comparison" class="section">
                <h2 class="section-title">游戏场景 vs 营销场景：需求大不同</h2>

                <p class="paragraph">
                    了解了这3个问题，我们再来看不同场景的需求差异：
                </p>

                <div class="chart-container">
                    <iframe src="../image/03-场景需求对比表.html" class="chart-iframe"></iframe>
                    <p class="chart-caption">游戏场景 vs 营销场景需求对比表</p>
                </div>

                <h3 class="section-subtitle">游戏场景特点</h3>

                <ul class="list">
                    <li class="list-item"><span class="emphasis">延迟要求：极其苛刻</span>（玩家容忍度接近零）</li>
                    <li class="list-item"><span class="emphasis">IP要求：相对宽松</span>（30-50个IP池轮流使用）</li>
                    <li class="list-item"><span class="emphasis">成本：极高</span>（10年前一条专线几十万/月）主要花在了线路上</li>
                </ul>

                <h3 class="section-subtitle">营销场景特点</h3>

                <ul class="list">
                    <li class="list-item"><span class="emphasis">延迟要求：相对宽松</span>（能流畅刷TK视频即可，直播除外）</li>
                    <li class="list-item"><span class="emphasis">IP要求：极其苛刻</span>（轻则0播放，重则封号）</li>
                    <li class="list-item"><span class="emphasis">成本：主要花在了独立IP上</span></li>
                </ul>

                <p class="paragraph">
                    理解这两件事的本质差异，再看开头提到的"水最深的就是IP"，就豁然开朗了。
                </p>

                <h3 class="section-subtitle">营销场景的简化逻辑</h3>

                <p class="paragraph">
                    还记得上面我们分析的网游加速器要解决的3个核心问题吗？
                </p>

                <ol class="list">
                    <li class="list-item"><span class="emphasis">IP地理限制</span> - 不能登录美服、韩服游戏</li>
                    <li class="list-item"><span class="emphasis">网络速度慢</span> - 延迟高经常丢包</li>
                    <li class="list-item"><span class="emphasis">IP质量问题</span> - 会被游戏服务器封号</li>
                </ol>

                <p class="paragraph">
                    在营销场景中，其实简化成了2个核心问题：
                </p>

                <ol class="list">
                    <li class="list-item"><span class="emphasis">IP地理限制</span> - 不能登录TikTok、Facebook等</li>
                    <li class="list-item"><span class="emphasis">IP质量问题</span> - 会被TK 0播放、封号</li>
                </ol>

                <div class="chart-container">
                    <iframe src="../image/07-营销场景问题简化图.html" class="chart-iframe"></iframe>
                    <p class="chart-caption">营销场景：从3个问题简化为2个问题</p>
                </div>

                <p class="paragraph">
                    为什么可以简化？因为营销场景对网络延迟的容忍度很高，200ms的延迟完全不影响刷视频、发帖等操作。
                </p>

                <h3 class="section-subtitle">解决IP地理限制</h3>

                <p class="paragraph">
                    在营销场景下可以采用直连方案，直接买对应国家的服务器进行搭建。
                </p>

                <p class="paragraph">
                    比如你做TK美区，直接买洛杉矶机房的服务器。做欧洲市场，买英国的服务器。做东南亚，直接买新加坡的服务器。
                </p>

                <p class="paragraph">
                    一般买一些知名的服务器服务商，比如搬瓦工，每个服务器会有一个独立的IP，这个IP不会跟别人共享使用。
                </p>

                <div class="code-block">┌─────────┐                              ┌─────────┐
│  你的   │                              │ 美国    │
│  设备   │─────────────────────────────>│ 服务器  │
└─────────┘                              └─────────┘
    🇨🇳                                      🇺🇸</div>
            </section>

            <section id="server-problem" class="section">
                <h2 class="section-title">1000个TK账号，需要买1000台服务器吗？</h2>

                <div class="chart-container">
                    <iframe src="../image/08-1000台服务器问题图.html" class="chart-iframe"></iframe>
                    <p class="chart-caption">1000台服务器的成本问题分析</p>
                </div>

                <p class="paragraph">
                    一个服务器是把CPU、内存、硬盘、流量打包在一起出售的。你想要多个IP就需要买多个服务器。
                </p>

                <p class="paragraph">
                    聪明的你是不是在想：买1000台服务器实在是太贵了。你只想要1000个IP，其他的CPU、内存、硬盘买一台服务器就行了。
                </p>

                <p class="paragraph">
                    所以能不能实现1台服务器+1000个IP呢？
                </p>

                <div class="quote">
                    答案是可以的。
                </div>
            </section>

            <section id="proxy-solution" class="section">
                <h2 class="section-title">代理IP服务商</h2>

                <p class="paragraph">
                    922S5proxy、ipipgo、cliproxy等等，这些都是专业的代理网络服务商。
                </p>

                <p class="paragraph">
                    比如美国的一个家庭住宅独立IP，一个月大概30元。
                </p>

                <div class="chart-container">
                    <iframe src="../image/09-代理服务商限制图.html" class="chart-iframe"></iframe>
                    <p class="chart-caption">代理服务商限制问题分析</p>
                </div>

                <p class="paragraph">
                    既然有这样的代理服务商，提供了美国的住宅IP，不能直接用这个IP吗？
                </p>

                <p class="paragraph">
                    不能。仔细看，这些代理IP服务商都会提示说<span class="highlight">"不接受中国IP直连"</span>。至于为什么，你猜。
                </p>

                <h3 class="section-subtitle">中转方案再次登场</h3>

                <p class="paragraph">
                    还记得上面说的网游加速器的第二个问题——网络速度吗？通过网络中转来解决了速度问题。
                </p>

                <p class="paragraph">
                    这里同样可以用网络中转来解决代理IP服务商不能直接使用的问题。通过中转IP先到某个国家，比如漂亮国，然后就可以使用这些代理IP了。
                </p>

                <div class="code-block">中国用户 ——> 海外中转 ——> 代理服务商 ——> 目标平台
   🇨🇳         🇭🇰          🇺🇸           🇺🇸</div>
            </section>

            <section id="chain-proxy" class="section">
                <h2 class="section-title">完整解决方案</h2>

                <p class="paragraph">
                    所以最终的方案只需要：
                </p>

                <ol class="list">
                    <li class="list-item"><span class="emphasis">买1个目标地区的服务器</span>（作为中转）</li>
                    <li class="list-item"><span class="emphasis">在代理IP平台买目标地区独立IP</span>（作为落地）</li>
                </ol>

                <p class="paragraph">
                    通过链式代理配置，就能实现一台手机一个独立IP。
                </p>

                <div class="chart-container">
                    <iframe src="../image/10-完整链式代理方案图.html" class="chart-iframe"></iframe>
                    <p class="chart-caption">完整链式代理方案架构图</p>
                </div>

                <p class="paragraph">
                    和管理大量手机卡一样，管理大量手机的网络也是个巨头疼的事情。
                </p>

                <p class="paragraph">
                    我做了一个工具来解决这个问题：
                </p>

                <ul class="list">
                    <li class="list-item"><span class="emphasis">中转网络管理界面</span>（已有截图，待添加）</li>
                    <li class="list-item"><span class="emphasis">落地网络管理界面</span>（已有截图，待添加）</li>
                </ul>
            </section>
        </main>
        
        <footer class="footer">
            <div class="footer-content">
                <h3 style="margin-bottom: 20px; font-size: 20px;">总结</h3>
                <p style="margin-bottom: 15px;">从网游加速器到出海营销，本质上都是在解决同样的3个问题：</p>
                <ul style="list-style: none; padding: 0; margin-bottom: 20px;">
                    <li style="margin-bottom: 8px;">1. IP地理限制</li>
                    <li style="margin-bottom: 8px;">2. 网络速度优化</li>
                    <li style="margin-bottom: 8px;">3. IP质量管理</li>
                </ul>
                <p style="margin-bottom: 15px;">关键是理解不同场景的需求差异：</p>
                <ul style="list-style: none; padding: 0; margin-bottom: 20px;">
                    <li style="margin-bottom: 8px;">• 游戏场景：追求极致速度，IP要求相对宽松</li>
                    <li style="margin-bottom: 8px;">• 营销场景：追求IP质量，速度要求相对宽松</li>
                </ul>
                <p style="margin-bottom: 20px;">而链式代理方案的真正价值，不是技术炫技，而是<strong>资源解耦带来的成本优化</strong>。</p>
                <p>理解了这个本质，你就能根据自己的需求选择合适的方案，不会被各种营销话术迷惑。</p>
                <hr style="margin: 30px 0; border: none; border-top: 1px solid rgba(255,255,255,0.3);">
                <p>如果你觉得这篇文章有价值，欢迎转发给需要的朋友。有问题也可以在评论区讨论。</p>
            </div>
        </footer>
    </div>
    
    <div class="progress-bar" id="progressBar"></div>
    <a href="#" class="back-to-top" id="backToTop">↑</a>

    <script>
        // 返回顶部功能
        window.addEventListener('scroll', function() {
            const backToTop = document.getElementById('backToTop');
            if (window.pageYOffset > 300) {
                backToTop.classList.add('visible');
            } else {
                backToTop.classList.remove('visible');
            }
        });

        document.getElementById('backToTop').addEventListener('click', function(e) {
            e.preventDefault();
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // 平滑滚动到锚点
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 阅读进度条
        window.addEventListener('scroll', function() {
            const windowHeight = window.innerHeight;
            const documentHeight = document.documentElement.scrollHeight;
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const progress = (scrollTop / (documentHeight - windowHeight)) * 100;
            document.getElementById('progressBar').style.width = progress + '%';
        });

        // 滚动动画
        function isElementInViewport(el) {
            const rect = el.getBoundingClientRect();
            return (
                rect.top >= 0 &&
                rect.left >= 0 &&
                rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
                rect.right <= (window.innerWidth || document.documentElement.clientWidth)
            );
        }

        function handleScrollAnimation() {
            const sections = document.querySelectorAll('.section');
            const charts = document.querySelectorAll('.chart-container');

            sections.forEach(section => {
                if (isElementInViewport(section)) {
                    section.classList.add('visible');
                }
            });

            charts.forEach(chart => {
                if (isElementInViewport(chart)) {
                    chart.classList.add('visible');
                }
            });
        }

        // 初始化
        window.addEventListener('load', function() {
            handleScrollAnimation();

            // 设置iframe高度
            document.querySelectorAll('.chart-iframe').forEach(iframe => {
                iframe.onload = function() {
                    this.style.height = this.contentWindow.document.body.scrollHeight + 'px';
                };
            });
        });

        window.addEventListener('scroll', handleScrollAnimation);
    </script>
</body>
</html>
