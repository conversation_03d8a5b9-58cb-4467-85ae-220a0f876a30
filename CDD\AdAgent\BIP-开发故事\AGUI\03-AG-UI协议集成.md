# 第3部分：AG-UI协议集成

## 🎯 AG-UI协议的作用

**AG-UI协议 = 标准化的包装器**
- 让你的分析逻辑能够与任何支持AG-UI的前端通信
- 提供标准的事件格式和流程
- 你专注核心分析，协议负责通信格式

## 🔄 为什么选择Streamable HTTP而不是WebSocket

基于你的分析文档，Streamable HTTP更适合广告Agent场景：

### 优势对比
- **更好的兼容性**：可以穿透防火墙和代理
- **更简单的部署**：利用现有HTTP基础设施
- **完美匹配场景**：主要是服务器向客户端推送分析结果
- **更低的复杂度**：不需要连接状态管理

## 🚀 后端实现：AG-UI事件包装器

### 核心包装器类

```python
# ag_ui_wrapper.py
from typing import Dict, Any, AsyncGenerator
import json
import time
from datetime import datetime

class AGUIWrapper:
    def __init__(self, llm_client, mcp_tools):
        self.llm = llm_client
        self.tools = mcp_tools
    
    async def process_query_stream(
        self, 
        user_query: str, 
        user_context: Dict[str, Any]
    ) -> AsyncGenerator[str, None]:
        """处理用户查询并返回AG-UI事件流"""
        
        thread_id = user_context.get("thread_id", "default")
        run_id = f"analysis_{int(time.time())}"
        
        try:
            # 1. 开始分析事件
            yield self._format_event({
                "type": "RunStarted",
                "threadId": thread_id,
                "runId": run_id
            })
            
            # 2. 调用数据查询工具
            async for event in self._call_data_tool_stream(user_query, run_id):
                yield self._format_event(event)
            
            # 3. 执行AI分析
            async for event in self._perform_analysis_stream(user_query, run_id):
                yield self._format_event(event)
            
            # 4. 生成可视化
            async for event in self._call_viz_tool_stream(user_query, run_id):
                yield self._format_event(event)
            
            # 5. 完成分析事件
            yield self._format_event({
                "type": "RunFinished",
                "threadId": thread_id,
                "runId": run_id
            })
            
        except Exception as e:
            yield self._format_event({
                "type": "RunError",
                "message": str(e),
                "code": "ANALYSIS_ERROR"
            })
    
    def _format_event(self, event: Dict[str, Any]) -> str:
        """格式化为SSE事件"""
        event["timestamp"] = datetime.utcnow().isoformat()
        return f"data: {json.dumps(event, ensure_ascii=False)}\n\n"
    
    async def _call_data_tool_stream(self, user_query: str, run_id: str) -> AsyncGenerator[Dict, None]:
        """调用数据查询工具并返回事件"""
        
        tool_call_id = f"data_query_{run_id}"
        
        # 开始工具调用
        yield {
            "type": "ToolCallStart",
            "toolCallId": tool_call_id,
            "toolCallName": "AdDataQueryMCP"
        }
        
        # 构建查询参数
        query_params = self._parse_query_params(user_query)
        
        yield {
            "type": "ToolCallArgs",
            "toolCallId": tool_call_id,
            "delta": json.dumps(query_params, ensure_ascii=False)
        }
        
        # 执行工具调用
        result = await self.tools["AdDataQueryMCP"].call_tool("query_ad_data", query_params)
        
        yield {
            "type": "ToolCallEnd",
            "toolCallId": tool_call_id
        }
        
        # 保存结果供后续使用
        self.current_data = json.loads(result[0].text)
    
    async def _perform_analysis_stream(self, user_query: str, run_id: str) -> AsyncGenerator[Dict, None]:
        """执行AI分析并流式返回结果"""
        
        message_id = f"analysis_{run_id}"
        
        # 开始消息
        yield {
            "type": "TextMessageStart",
            "messageId": message_id,
            "role": "assistant"
        }
        
        # 构建分析提示词
        analysis_prompt = self._build_analysis_prompt(user_query, self.current_data)
        
        # 流式调用LLM
        async for chunk in self.llm.stream_analyze(analysis_prompt):
            yield {
                "type": "TextMessageContent",
                "messageId": message_id,
                "delta": chunk
            }
        
        # 结束消息
        yield {
            "type": "TextMessageEnd",
            "messageId": message_id
        }
    
    async def _call_viz_tool_stream(self, user_query: str, run_id: str) -> AsyncGenerator[Dict, None]:
        """调用可视化工具"""
        
        # 判断是否需要可视化
        if not self._need_visualization(user_query):
            return
        
        tool_call_id = f"viz_{run_id}"
        
        yield {
            "type": "ToolCallStart",
            "toolCallId": tool_call_id,
            "toolCallName": "VisualizationMCP"
        }
        
        viz_params = {
            "data": self.current_data,
            "viz_type": self._determine_viz_type(user_query),
            "title": "广告数据分析结果"
        }
        
        yield {
            "type": "ToolCallArgs",
            "toolCallId": tool_call_id,
            "delta": json.dumps(viz_params, ensure_ascii=False)
        }
        
        # 执行可视化生成
        viz_result = await self.tools["VisualizationMCP"].call_tool("generate_visualization", viz_params)
        
        yield {
            "type": "ToolCallEnd",
            "toolCallId": tool_call_id
        }
        
        # 发送可视化结果
        yield {
            "type": "Custom",
            "name": "VisualizationReady",
            "value": json.loads(viz_result[0].text)
        }
    
    def _parse_query_params(self, user_query: str) -> Dict[str, Any]:
        """从用户查询中解析参数"""
        # 这里可以使用简单的关键词匹配或者调用LLM来解析
        params = {
            "user_id": "current_user",  # 从上下文获取
            "date_range": "最近30天",   # 默认值
            "platform": "all",
            "keywords": [],
            "limit": 100
        }
        
        # 简单的关键词提取
        if "最近" in user_query and "天" in user_query:
            # 提取天数
            import re
            days = re.findall(r'最近(\d+)天', user_query)
            if days:
                params["date_range"] = f"最近{days[0]}天"
        
        # 提取平台信息
        platforms = ["Facebook", "TikTok", "小红书", "抖音"]
        for platform in platforms:
            if platform in user_query:
                params["platform"] = platform
                break
        
        return params
    
    def _build_analysis_prompt(self, user_query: str, ad_data: Dict) -> str:
        """构建分析提示词"""
        return f"""
你是专业的广告数据分析师，基于用户的个人广告数据进行分析。

用户问题：{user_query}

广告数据概况：
- 总数量：{ad_data.get('total_count', 0)}
- 数据范围：{ad_data.get('query_info', {})}

请基于实际数据进行分析，重点关注：
1. 新兴商业机会的识别
2. 行业趋势的变化
3. 具体的可行性建议

请用结构化、易读的格式输出分析结果。
        """
    
    def _need_visualization(self, user_query: str) -> bool:
        """判断是否需要生成可视化"""
        viz_keywords = ["图表", "可视化", "趋势图", "对比", "分析图"]
        return any(keyword in user_query for keyword in viz_keywords)
    
    def _determine_viz_type(self, user_query: str) -> str:
        """确定可视化类型"""
        if "趋势" in user_query:
            return "trend_chart"
        elif "对比" in user_query:
            return "comparison_chart"
        else:
            return "opportunity_dashboard"
```

### FastAPI服务器

```python
# main.py
from fastapi import FastAPI, Request
from fastapi.responses import StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
import json
from ag_ui_wrapper import AGUIWrapper

app = FastAPI(title="广告数据分析Agent")

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 初始化AG-UI包装器
wrapper = AGUIWrapper(llm_client, mcp_tools)

@app.post("/ag-ui/stream")
async def ag_ui_stream(request: Request):
    """AG-UI协议Streamable HTTP端点"""
    
    data = await request.json()
    user_query = data.get("content", "")
    user_context = data.get("context", {})
    
    return StreamingResponse(
        wrapper.process_query_stream(user_query, user_context),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/plain; charset=utf-8"
        }
    )

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

## 🎨 前端实现：CopilotKit集成

### React前端组件

```typescript
// App.tsx
import React from 'react';
import { CopilotKit, CopilotChat } from "@copilotkit/react-core";
import { CopilotSidebar } from "@copilotkit/react-ui";

function App() {
  return (
    <CopilotKit 
      runtimeUrl="http://localhost:8000/ag-ui/stream"
      agent="ad-analysis-agent"
    >
      <div className="app">
        <header className="app-header">
          <h1>广告机会分析助手</h1>
          <p>基于您的个人广告数据发现商业机会</p>
        </header>
        
        <CopilotSidebar>
          <CopilotChat 
            instructions="我是您的广告数据分析助手，可以帮您从个人广告数据中发现商业机会、分析行业趋势、监控竞品动态。"
            placeholder="问我关于您看到的广告中的商业机会..."
            showInProgress={true}
            makeSystemMessage={(message) => `分析请求：${message}`}
          />
        </CopilotSidebar>
      </div>
    </CopilotKit>
  );
}

export default App;
```

### 自定义事件处理

```typescript
// CustomEventHandler.tsx
import { useCopilotAction } from "@copilotkit/react-core";

export function CustomEventHandler() {
  // 处理可视化就绪事件
  useCopilotAction({
    name: "handle_visualization_ready",
    description: "处理可视化内容就绪事件",
    parameters: [
      {
        name: "visualization_data",
        type: "object",
        description: "可视化数据"
      }
    ],
    handler: async ({ visualization_data }) => {
      // 在页面中显示可视化内容
      const vizContainer = document.getElementById('visualization-container');
      if (vizContainer && visualization_data.html_url) {
        vizContainer.innerHTML = `
          <iframe 
            src="${visualization_data.html_url}" 
            width="100%" 
            height="500px"
            frameborder="0">
          </iframe>
        `;
      }
    }
  });

  return (
    <div id="visualization-container" className="visualization-container">
      {/* 可视化内容将在这里显示 */}
    </div>
  );
}
```

## 📦 部署配置

### Docker配置

```dockerfile
# Dockerfile
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### docker-compose.yml

```yaml
version: '3.8'

services:
  ad-analysis-agent:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**********************************/addb
      - REDIS_URL=redis://redis:6379
    depends_on:
      - db
      - redis

  db:
    image: postgres:15
    environment:
      POSTGRES_DB: addb
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    
  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    depends_on:
      - ad-analysis-agent

volumes:
  postgres_data:
```

## 🚀 快速启动

### 1. 启动后端服务
```bash
# 启动所有服务
docker-compose up -d

# 或者本地开发
pip install -r requirements.txt
uvicorn main:app --reload
```

### 2. 启动前端
```bash
cd frontend
npm install
npm start
```

### 3. 测试连接
```bash
# 测试健康检查
curl http://localhost:8000/health

# 测试AG-UI端点
curl -X POST http://localhost:8000/ag-ui/stream \
  -H "Content-Type: application/json" \
  -d '{"content": "最近有什么新的商业机会？", "context": {"thread_id": "test"}}'
```

## 🎯 核心优势总结

1. **专注核心**：你只需要关注提示词和MCP工具的质量
2. **标准化**：使用AG-UI协议，前端可以复用现有方案
3. **简单部署**：基于HTTP的简单架构，易于部署和维护
4. **易于扩展**：添加新的分析场景只需要更新提示词
5. **用户体验**：提供专业的对话式交互体验

这样你就有了一个完整的、基于AG-UI协议的广告数据分析Agent系统！
