<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>成本对比分析图</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .title {
            text-align: center;
            font-size: 28px;
            color: #2c3e50;
            margin-bottom: 40px;
            font-weight: bold;
        }
        
        .charts-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .chart-box {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }
        
        .chart-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .chart-canvas {
            max-height: 300px;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
        }
        
        .comparison-table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 16px;
            font-weight: bold;
        }
        
        .comparison-table td {
            padding: 18px;
            text-align: center;
            border-bottom: 1px solid #ecf0f1;
            font-size: 14px;
        }
        
        .comparison-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .comparison-table tr:hover {
            background-color: #e3f2fd;
            transition: background-color 0.3s ease;
        }
        
        .cost-item {
            background: #ecf0f1 !important;
            font-weight: bold;
            color: #2c3e50;
            text-align: left;
            padding-left: 25px;
        }
        
        .traditional-cost {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            font-weight: bold;
        }
        
        .proxy-cost {
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
            color: white;
            font-weight: bold;
        }
        
        .savings-highlight {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            font-weight: bold;
            font-size: 16px;
        }
        
        .roi-analysis {
            margin-top: 40px;
            background: #ecf0f1;
            border-radius: 10px;
            padding: 25px;
        }
        
        .roi-title {
            font-size: 20px;
            color: #2c3e50;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .roi-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
        }
        
        .roi-box {
            background: white;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        
        .roi-number {
            font-size: 32px;
            font-weight: bold;
            color: #27ae60;
            margin-bottom: 10px;
        }
        
        .roi-label {
            font-size: 14px;
            color: #7f8c8d;
        }
        
        .scale-analysis {
            margin-top: 40px;
        }
        
        .scale-title {
            font-size: 20px;
            color: #2c3e50;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .scale-chart-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }
        
        .insights {
            margin-top: 40px;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
        }
        
        .insights-title {
            font-size: 20px;
            color: #2c3e50;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .insight-item {
            background: white;
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        
        .insight-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 8px;
        }
        
        .insight-desc {
            color: #7f8c8d;
            font-size: 14px;
        }
        
        .emoji {
            font-size: 18px;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="title">成本对比分析：传统VPS vs 链式代理</div>
        
        <div class="charts-container">
            <div class="chart-box">
                <div class="chart-title">月度成本对比</div>
                <canvas id="costChart" class="chart-canvas"></canvas>
            </div>
            
            <div class="chart-box">
                <div class="chart-title">成本构成分析</div>
                <canvas id="compositionChart" class="chart-canvas"></canvas>
            </div>
        </div>
        
        <table class="comparison-table">
            <thead>
                <tr>
                    <th style="width: 25%;">成本项目</th>
                    <th style="width: 30%;">传统VPS方案</th>
                    <th style="width: 30%;">链式代理方案</th>
                    <th style="width: 15%;">节省金额</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="cost-item">VPS服务器</td>
                    <td class="traditional-cost">
                        1000台 × $10/月<br>
                        = $10,000/月
                    </td>
                    <td class="proxy-cost">
                        1台 × $20/月<br>
                        = $20/月
                    </td>
                    <td class="savings-highlight">$9,980</td>
                </tr>
                <tr>
                    <td class="cost-item">IP资源</td>
                    <td class="traditional-cost">
                        包含在VPS内<br>
                        $0/月
                    </td>
                    <td class="proxy-cost">
                        1000个 × $2/月<br>
                        = $2,000/月
                    </td>
                    <td style="color: #e74c3c;">+$2,000</td>
                </tr>
                <tr>
                    <td class="cost-item">技术维护</td>
                    <td class="traditional-cost">
                        1000台服务器维护<br>
                        $1,000/月
                    </td>
                    <td class="proxy-cost">
                        1台服务器维护<br>
                        $100/月
                    </td>
                    <td class="savings-highlight">$900</td>
                </tr>
                <tr>
                    <td class="cost-item">总成本</td>
                    <td class="traditional-cost">$11,000/月</td>
                    <td class="proxy-cost">$2,120/月</td>
                    <td class="savings-highlight">$8,880</td>
                </tr>
                <tr>
                    <td class="cost-item">节省比例</td>
                    <td colspan="2" style="text-align: center; font-weight: bold; color: #2c3e50;">
                        节省成本：80.7%
                    </td>
                    <td class="savings-highlight">80.7%</td>
                </tr>
            </tbody>
        </table>
        
        <div class="roi-analysis">
            <div class="roi-title">投资回报率分析</div>
            
            <div class="roi-grid">
                <div class="roi-box">
                    <div class="roi-number">80.7%</div>
                    <div class="roi-label">成本节省比例</div>
                </div>
                
                <div class="roi-box">
                    <div class="roi-number">$8,880</div>
                    <div class="roi-label">月度节省金额</div>
                </div>
                
                <div class="roi-box">
                    <div class="roi-number">$106,560</div>
                    <div class="roi-label">年度节省金额</div>
                </div>
            </div>
        </div>
        
        <div class="scale-analysis">
            <div class="scale-title">规模效应分析</div>
            <div class="scale-chart-container">
                <canvas id="scaleChart" style="max-height: 400px;"></canvas>
            </div>
        </div>
        
        <div class="insights">
            <div class="insights-title">💡 成本优化洞察</div>
            
            <div class="insight-item">
                <div class="insight-title">
                    <span class="emoji">📊</span>规模效应明显
                </div>
                <div class="insight-desc">
                    账号数量越多，链式代理的成本优势越明显。当账号数量超过100个时，成本节省就非常显著。
                </div>
            </div>
            
            <div class="insight-item">
                <div class="insight-title">
                    <span class="emoji">🔄</span>资源利用率提升
                </div>
                <div class="insight-desc">
                    传统VPS方案的资源利用率不到5%，而链式代理方案可以达到80%以上，大幅提高资源效率。
                </div>
            </div>
            
            <div class="insight-item">
                <div class="insight-title">
                    <span class="emoji">⚡</span>维护成本降低
                </div>
                <div class="insight-desc">
                    从维护1000台服务器降低到维护1台服务器，运维复杂度和人力成本大幅下降。
                </div>
            </div>
            
            <div class="insight-item">
                <div class="insight-title">
                    <span class="emoji">🎯</span>专业化分工
                </div>
                <div class="insight-desc">
                    VPS厂商专注计算资源，代理商专注IP资源，各自发挥专业优势，整体效率更高。
                </div>
            </div>
            
            <div class="insight-item">
                <div class="insight-title">
                    <span class="emoji">📈</span>投资回报率高
                </div>
                <div class="insight-desc">
                    年度节省超过10万美元，对于中小企业来说，这笔节省的资金可以投入到业务发展中。
                </div>
            </div>
        </div>
    </div>

    <script>
        // 月度成本对比图
        const costCtx = document.getElementById('costChart').getContext('2d');
        new Chart(costCtx, {
            type: 'bar',
            data: {
                labels: ['传统VPS方案', '链式代理方案'],
                datasets: [{
                    label: '月度成本 (USD)',
                    data: [11000, 2120],
                    backgroundColor: [
                        'rgba(255, 107, 107, 0.8)',
                        'rgba(78, 205, 196, 0.8)'
                    ],
                    borderColor: [
                        'rgba(255, 107, 107, 1)',
                        'rgba(78, 205, 196, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });

        // 成本构成分析图
        const compositionCtx = document.getElementById('compositionChart').getContext('2d');
        new Chart(compositionCtx, {
            type: 'doughnut',
            data: {
                labels: ['VPS服务器', 'IP资源', '技术维护'],
                datasets: [{
                    data: [20, 2000, 100],
                    backgroundColor: [
                        'rgba(69, 183, 209, 0.8)',
                        'rgba(150, 201, 61, 0.8)',
                        'rgba(102, 126, 234, 0.8)'
                    ],
                    borderColor: [
                        'rgba(69, 183, 209, 1)',
                        'rgba(150, 201, 61, 1)',
                        'rgba(102, 126, 234, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // 规模效应分析图
        const scaleCtx = document.getElementById('scaleChart').getContext('2d');
        const accountNumbers = [10, 50, 100, 500, 1000, 2000];
        const traditionalCosts = accountNumbers.map(n => n * 10);
        const proxyCosts = accountNumbers.map(n => 20 + n * 2);

        new Chart(scaleCtx, {
            type: 'line',
            data: {
                labels: accountNumbers,
                datasets: [{
                    label: '传统VPS方案',
                    data: traditionalCosts,
                    borderColor: 'rgba(255, 107, 107, 1)',
                    backgroundColor: 'rgba(255, 107, 107, 0.1)',
                    borderWidth: 3,
                    fill: true
                }, {
                    label: '链式代理方案',
                    data: proxyCosts,
                    borderColor: 'rgba(78, 205, 196, 1)',
                    backgroundColor: 'rgba(78, 205, 196, 0.1)',
                    borderWidth: 3,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top'
                    },
                    title: {
                        display: true,
                        text: '不同账号规模下的成本对比'
                    }
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: '账号数量'
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: '月度成本 (USD)'
                        },
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
