<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学习模式革命</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .glass-card {
            background: rgba(255,255,255,0.15);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255,255,255,0.3);
        }
        
        .mode-card {
            transition: all 0.3s ease;
        }
        
        .mode-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }
        
        .old-mode {
            border-color: #f59e0b;
            background: linear-gradient(135deg, rgba(245,158,11,0.2) 0%, rgba(217,119,6,0.1) 100%);
        }
        
        .new-mode {
            border-color: #06b6d4;
            background: linear-gradient(135deg, rgba(6,182,212,0.2) 0%, rgba(8,145,178,0.1) 100%);
        }
        
        .step-item {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
        }
        
        .step-item:hover {
            background: rgba(255,255,255,0.2);
            transform: translateX(5px);
        }
        
        .arrow-flow {
            font-size: 2rem;
            color: rgba(255,255,255,0.8);
        }
        
        @keyframes flow {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }
        
        .flowing {
            animation: flow 2s ease-in-out infinite;
        }
        
        .flowing:nth-child(2) { animation-delay: 0.5s; }
        .flowing:nth-child(3) { animation-delay: 1s; }
        .flowing:nth-child(4) { animation-delay: 1.5s; }
    </style>
</head>
<body class="gradient-bg">
    <div class="container mx-auto px-6 py-12">
        <!-- 标题 -->
        <div class="text-center mb-16">
            <h1 class="text-5xl font-bold text-white mb-6">
                学习模式革命
            </h1>
            <h2 class="text-3xl font-semibold text-white/90 mb-4">
                第一个变化：先做再学
            </h2>
            <p class="text-xl text-white/80 max-w-3xl mx-auto">
                过去的学习和工作模式完全可以反过来
            </p>
        </div>
        
        <!-- 两种模式对比 -->
        <div class="grid lg:grid-cols-2 gap-12 max-w-7xl mx-auto mb-16">
            <!-- 传统模式：先学再做 -->
            <div class="mode-card old-mode glass-card rounded-3xl p-8">
                <div class="text-center mb-8">
                    <div class="text-6xl mb-4">📚</div>
                    <h3 class="text-2xl font-bold text-white mb-2">传统模式</h3>
                    <h4 class="text-xl text-amber-200 font-semibold">先学，再做</h4>
                </div>
                
                <div class="space-y-4">
                    <div class="step-item rounded-xl p-4 flowing">
                        <div class="flex items-center">
                            <span class="text-2xl mr-3">1️⃣</span>
                            <div>
                                <div class="text-white font-semibold">学习编程语言</div>
                                <div class="text-white/70 text-sm">掌握语法、框架基础</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center arrow-flow flowing">↓</div>
                    
                    <div class="step-item rounded-xl p-4 flowing">
                        <div class="flex items-center">
                            <span class="text-2xl mr-3">2️⃣</span>
                            <div>
                                <div class="text-white font-semibold">学习开发工具</div>
                                <div class="text-white/70 text-sm">IDE、调试、部署等</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center arrow-flow flowing">↓</div>
                    
                    <div class="step-item rounded-xl p-4 flowing">
                        <div class="flex items-center">
                            <span class="text-2xl mr-3">3️⃣</span>
                            <div>
                                <div class="text-white font-semibold">学习架构设计</div>
                                <div class="text-white/70 text-sm">数据库、API、系统设计</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center arrow-flow flowing">↓</div>
                    
                    <div class="step-item rounded-xl p-4 flowing">
                        <div class="flex items-center">
                            <span class="text-2xl mr-3">4️⃣</span>
                            <div>
                                <div class="text-white font-semibold">开始动手做</div>
                                <div class="text-white/70 text-sm">学得差不多了才敢开始</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-6 bg-amber-500/20 rounded-xl p-4">
                    <p class="text-amber-200 font-semibold text-center">⏰ 学习周期：几个月到几年</p>
                </div>
            </div>
            
            <!-- 新模式：先做再学 -->
            <div class="mode-card new-mode glass-card rounded-3xl p-8">
                <div class="text-center mb-8">
                    <div class="text-6xl mb-4">🚀</div>
                    <h3 class="text-2xl font-bold text-white mb-2">AI时代模式</h3>
                    <h4 class="text-xl text-cyan-200 font-semibold">先做完，再回头学</h4>
                </div>
                
                <div class="space-y-4">
                    <div class="step-item rounded-xl p-4 flowing">
                        <div class="flex items-center">
                            <span class="text-2xl mr-3">1️⃣</span>
                            <div>
                                <div class="text-white font-semibold">第一组提示词</div>
                                <div class="text-white/70 text-sm">需求 + 架构 → 生成代码</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center arrow-flow flowing">↓</div>
                    
                    <div class="step-item rounded-xl p-4 flowing">
                        <div class="flex items-center">
                            <span class="text-2xl mr-3">2️⃣</span>
                            <div>
                                <div class="text-white font-semibold">AI生成完整项目</div>
                                <div class="text-white/70 text-sm">后端代码 + 项目结构</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center arrow-flow flowing">↓</div>
                    
                    <div class="step-item rounded-xl p-4 flowing">
                        <div class="flex items-center">
                            <span class="text-2xl mr-3">3️⃣</span>
                            <div>
                                <div class="text-white font-semibold">第二组提示词</div>
                                <div class="text-white/70 text-sm">代码学习 + 原理讲解</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center arrow-flow flowing">↓</div>
                    
                    <div class="step-item rounded-xl p-4 flowing">
                        <div class="flex items-center">
                            <span class="text-2xl mr-3">4️⃣</span>
                            <div>
                                <div class="text-white font-semibold">理解实现原理</div>
                                <div class="text-white/70 text-sm">看文档理解AI做了什么</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-6 bg-cyan-500/20 rounded-xl p-4">
                    <p class="text-cyan-200 font-semibold text-center">⚡ 完成周期：几天到几周</p>
                </div>
            </div>
        </div>
        
        <!-- 核心洞察 -->
        <div class="text-center mb-12">
            <div class="glass-card rounded-2xl p-8 max-w-5xl mx-auto">
                <h3 class="text-3xl font-bold text-white mb-6">
                    💡 核心洞察
                </h3>
                <div class="grid md:grid-cols-2 gap-8">
                    <div class="bg-white/10 rounded-xl p-6">
                        <h4 class="text-xl font-bold text-amber-300 mb-3">传统思维</h4>
                        <p class="text-white/80 text-left">
                            • 必须先掌握技能才能做事<br>
                            • 学习是做事的前置条件<br>
                            • 害怕不懂就不敢动手<br>
                            • 完美主义拖延症
                        </p>
                    </div>
                    <div class="bg-white/10 rounded-xl p-6">
                        <h4 class="text-xl font-bold text-cyan-300 mb-3">AI时代思维</h4>
                        <p class="text-white/80 text-left">
                            • 先让AI帮你做出来<br>
                            • 再通过结果反推学习<br>
                            • 从具体到抽象的理解<br>
                            • 快速验证想法可行性
                        </p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 类比说明 -->
        <div class="text-center">
            <div class="glass-card rounded-2xl p-8 max-w-4xl mx-auto">
                <h3 class="text-2xl font-bold text-white mb-4">
                    🎯 高管思维
                </h3>
                <p class="text-xl text-white/90 leading-relaxed mb-4">
                    仔细想想也不奇怪。如果你做过高管就懂。<br>
                    做一个新业务，最快的方式是：
                </p>
                <div class="bg-white/10 rounded-xl p-6">
                    <p class="text-lg text-white/90">
                        <span class="text-yellow-300 font-bold">花钱招一个懂行的人</span> → 
                        <span class="text-blue-300 font-bold">让他做出来</span> → 
                        <span class="text-green-300 font-bold">让他汇报</span> → 
                        <span class="text-purple-300 font-bold">直到自己理解</span>
                    </p>
                </div>
                <p class="text-lg text-white/80 mt-4">
                    AI，就是那个你不需要付高薪，就能7x24小时随时请教的"专家团队"
                </p>
            </div>
        </div>
    </div>
</body>
</html>
