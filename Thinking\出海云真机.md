# 项目:以TK短视频带货业务为切入点,在业务中遇到的问题,做成工具。



# 出海云真机
**概述**: 在真机安装APK,通过clash 链式代理实现干净、可控的IP环境 ,以及SIM卡的管理。

# 方向性关键决策

## 1.AB模型-行业机会
A状态（当前）：
海外APP风控较弱
ARM批量注册可行
云机方案占主导
云机有完整配套工具

B状态（未来）：
海外APP风控加强
ARM批量注册受限
真机方案成为必需
真机缺乏配套工具



## 2.目标客户群体定义
主要客户是谁？跨境电商从业者
客户规模分层：小微企业(1-10台) 和 中型企业(10-100)
客户获取成本(CAC)预期是多少？ 定价的30%


##  3.MVP的核心价值
做什么：  通过clash 链式代理 实现 干净、可控的IP环境
不做什么： MVP阶段不提供任何自动化养号、操作等RPA功能。所有应用内的操作，均由用户手动完成。


##  4.定价
 体验套餐 免费1台
 套餐A (10台): 266元/月，单价26.6元/台 (约9折)
 套餐B (30台): 660元/月，单价22.0元/台 (约7.3折)
 套餐C (100台): 1600元/月，单价16.0元/台 (约5.3折)



# 产品侧关键决策

## 用户故事
  作为一名出海运营者，我希望在手机上安装一个APK,可以和后台绑定。在后台上，能设置手机的中转网络和落地网络。可以解决 1.目标国家的IP 2.目标国家的静态IP 
  3.整体速度。  4.自由选择IP服务商。
 
## 产品原型
  待生成产品原型

# Part1 - CDD
1. 出海手机如何选择? 需要出厂GSM 
2. 海外手机卡如何解决？ 9esim
  9esim  可以绑定50个号  解决手机号问题
  有了手机号 可以注册Gmail等

3. 如何选择代理IP? 
  代理IP快速理解 ISP和住宅  为什么要用独立IP如何选择 成本 引入链式代理概念
  
4. 产品化方案 如何在后台一键配置。

# 技术侧关键决策

##   TPF任务清单
  1.Clash二开-实现远程配置
  2.安卓端和后端通信
  3.安卓设备和后台用户绑定



   



# Todo-Tech
1.Clash链式代理配置  ✅
2.ClashMetaForAndroid 源码看一遍 ✅
3.设备绑定集成 -安卓端&服务端
4 steamhttp集成-安卓端&服务端
5.确定后台操作逻辑
6.URL配置远程更新








