
01. 痛点场景开头 - IP代理的普遍问题
  出海三件套,手机,卡,IP。水最深的就是IP了吧。0播放了,掉橱窗了什么问题都可以让IP背下锅。同时市面上又有各种各样的解决方案,小火箭,软路由,SDWAN,专线,机房IP,专线IP. 价格也是个谜,从1个月10几块到一个月1000-2000。价格跨度巨大，对于小白来说,肉眼看起来好像又没有什么区别。


02. 故事引入 - 网游加速器经历
  14-15年我加入一家网游加速器公司,当时是TOP3的加速器。负责搞流量。
网游加速器这个产品是怎么来的？
我隐约还记得当时有两款游戏英雄联盟 和 暗黑破坏神3 的营收占了整个加速器大盘的80%以上。
对于网游爱好者来说
《英雄联盟》- 追求竞技巅峰：
水平更高： 2014-2015年，韩服（KR）是公认的LOL“圣地”，职业选手和顶尖路人云集。去韩服打Rank是证明自己、提升技术的最佳途径。
风气更好： 普遍认为外服（尤其是韩服）的游戏环境更纯粹，玩家求胜欲强，很少有国服当时常见的“演员”和“20投”现象。
主播效应： 当时游戏直播刚刚兴起，大主播们“征战韩服”是重要的直播内容，吸引了大量粉丝模仿，也想去体验一下。

《暗黑破坏神3》- 被迫的选择与更好的体验：
国服上线晚： 《暗黑破坏神3》全球发售是2012年，而其资料片《夺魂之镰》是2014年3月上线的。但国服直到2015年4月才正式公测。在这之前，中国玩家想玩这款大作，唯一途径就是去玩亚服、美服或欧服。
原汁原味： 很多玩家担心国服会有内容上的“和谐”（比如血腥效果变成黑色石油），因此即便国服上线后，也选择留在外服，体验“原汁原味”的版本。
赛季模式： 暗黑3的赛季模式是核心玩法，全球同步开启。想和全球玩家一起“开荒”，就必须去外服。

技术端：为什么不挂加速器就没法玩？
有了需求，但现实的物理和网络障碍是巨大的。不使用加速器，直连外服会遇到以下致命问题：
物理距离导致的硬伤：高延迟 (High Latency/Ping)
数据传输速度受光速限制。从中国到美国/欧洲的游戏服务器，数据一来一回，天然就会产生150ms - 300ms的延迟。
这是什么概念？
LOL里，你的技能会慢0.2秒放出，你看到的敌人位置其实是0.2秒前的，你永远躲不掉关键技能，体验极差。
暗黑3里，尤其是在专家模式（Hardcore），一个延迟就可能让你反应不及，导致角色永久死亡，所有心血付诸东流。
跨国公网的顽疾：严重丢包 (Packet Loss)
你的游戏数据在去往国外服务器的路上，要经过无数个公共网络节点，尤其要挤过那个拥堵不堪的“国际出口网关”。
这就好比高峰期开车，不仅开得慢（高延迟），还随时可能因为堵死而“丢车”（丢包）。
游戏里丢包的表现就是： 人物瞬移、卡在原地不动、技能按了没反应、突然掉线。这比稳定的高延迟更让人抓狂。


03. 认知升级 - 10年后的重新思考
  10年前我主要搞流量。后来做了运营,干了电商，成了一个全栈开发。
  最近和当年的技术团队再沟通 聊了聊网游加速器的技术实现,有了技术背景，沟通起来就顺畅的多。猛然想起当年开会,讨论的技术问题,现在竟然又能理解了。

04. 技术原理 - 集中讲清楚链式代理
  链式代理就像寄快递的中转站：
- 你的请求先到中转服务器（比如香港）
- 中转服务器再转发到目标服务器（比如美国TikTok）



05. 方案对比 - 游戏场景 vs 营销场景的需求差异
游戏场景对延迟要求是极其苛刻的,对IP的要求通常没那么高，一个IP池 大概30-50个 大家轮流使用就行。只有个别游戏对IP风控特别严格。

营销场景对延迟的要求就没那么高了,能流畅的刷TK的视频就行了。直播除外，直播的要求就近似于网游加速器了。对IP的要求反而是极其苛刻的。轻则0播放,重则封号。


06. 解决方案 - 具体的技术架构和实现
原理都是 链式代理
网游加速器的场景对延迟要求是极其苛刻的。
没办法用公网服务器,需要拉专线,比如中美专线,中港专线。来解决延迟的问题。

营销的场景对延迟要求没那么苛刻,就可以用
公网服务器
但对IP极其苛刻，这时候用SOCKS5代理。


07. 要多少钱 - 成本、效果。
   比如做美国
   1是一个美国的公网服务器
   2是socks5代理价格

     
08. 产品展示
1，如何配置中转网络
2. 如何配置落地网络