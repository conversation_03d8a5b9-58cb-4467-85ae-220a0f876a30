01. 网游加速器的故事
  14-15年加入一家网游加速器公司
  当时是TOP3的加速器

  网游加速器核心解决3个问题
  1.能登录美服,日服游戏.
  2.能低延迟不丢包.能玩
  3.不封号。



02. 10年后 和当年的技术团队再沟通
    10年前我是负责流量  10年后我成了一个全栈开发

03.  彻底了解网游加速器
网络游戏是对延迟要求极其苛刻的场景。

心原理都是 链式代理/
但链式代理的效果好坏 
1.取决于线路质量  比如专线 延迟就低
2.取决于智能路由  比如 能动态选择
3.取决于私有协议    
     
04. 不需要搞专线
延迟要求不是极其苛刻的。并不需要走专线

05. 对IP的独特需求
营销场景对IP的要求是极其苛刻

06. 私域场景的方案
公网服务器+SOcKT5代理

07. 工具展示
   在后台绑定了安卓设备
   设置中转网络 和 落地网络  实现链式代理
IP管理后台系统
简介:集中管理安卓手机网络.


功能1:网络设置
     中转网络配置
     落地网络配置
     用户必须先配置中转网络
     所有的落地网络都是基于中转网络的


功能2:安卓手机绑定
    安卓手机装上APP,打开显示6位数字框。
    在后台获取到输入6位数字,输入即可绑定手机。
    绑定手机后即可动态下发网络配置。
    
 